<?php
// Database connection test

// Database credentials
define('DB_HOST', 'localhost');
define('DB_NAME', 'exam_seating');
define('DB_USER', 'root');
define('DB_PASS', '');

// Test connection
try {
    $conn = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to MySQL server successfully!<br>";
    
    // Check if database exists
    $stmt = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '" . DB_NAME . "'");
    $dbExists = $stmt->fetchColumn();
    
    if ($dbExists) {
        echo "Database '" . DB_NAME . "' exists.<br>";
        
        // Connect to the database
        $dbConn = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
        $dbConn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "Connected to database '" . DB_NAME . "' successfully!<br>";
        
        // Check if tables exist
        $tables = array('users', 'authorized_users', 'courses', 'exams', 'rooms', 'students', 'seating_arrangements', 'seats');
        $existingTables = array();
        
        foreach ($tables as $table) {
            $stmt = $dbConn->query("SHOW TABLES LIKE '" . $table . "'");
            if ($stmt->rowCount() > 0) {
                $existingTables[] = $table;
            }
        }
        
        if (count($existingTables) > 0) {
            echo "Existing tables: " . implode(', ', $existingTables) . "<br>";
        } else {
            echo "No tables found. You need to initialize the database.<br>";
        }
    } else {
        echo "Database '" . DB_NAME . "' does not exist. You need to create it.<br>";
    }
} catch(PDOException $e) {
    echo "Connection failed: " . $e->getMessage();
}
?>
