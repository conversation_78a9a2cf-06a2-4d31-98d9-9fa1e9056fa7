/**
 * Rooms Module
 * Handles room management functionality
 */

// Initialize rooms module
function initRooms() {
    // Add event listener for room form
    const roomForm = document.getElementById('roomForm');
    if (roomForm) {
        roomForm.addEventListener('submit', addRoom);
    }
    
    // Render rooms table
    renderRoomsTable();
}

// Add new room
function addRoom(e) {
    e.preventDefault();
    
    const rows = parseInt(document.getElementById('roomRows').value);
    const columns = parseInt(document.getElementById('roomColumns').value);
    const studentsPerBench = parseInt(document.getElementById('studentsPerBench').value);
    
    // Validate students per bench (max 4)
    if (studentsPerBench < 1 || studentsPerBench > 4) {
        alert('Students per bench must be between 1 and 4');
        return;
    }
    
    const rooms = window.Data.getData('rooms');
    
    const room = {
        id: rooms.length > 0 ? Math.max(...rooms.map(r => r.id)) + 1 : 1,
        name: document.getElementById('roomName').value,
        rows: rows,
        columns: columns,
        studentsPerBench: studentsPerBench,
        capacity: rows * columns * studentsPerBench,
        teacher: document.getElementById('teacherName').value
    };
    
    rooms.push(room);
    window.Data.saveData('rooms', rooms);
    
    renderRoomsTable();
    
    // Reset form
    document.getElementById('roomForm').reset();
    // Set default value for students per bench
    document.getElementById('studentsPerBench').value = 1;
    
    // Reload dropdowns
    window.Dashboard.loadRoomsDropdown('visualRoom');
}

// Render rooms table
function renderRoomsTable() {
    const table = document.getElementById('roomsTable');
    if (!table) return;
    
    const tbody = table.querySelector('tbody');
    tbody.innerHTML = '';
    
    const rooms = window.Data.getData('rooms');
    
    rooms.forEach(room => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${room.name}</td>
            <td>${room.rows}</td>
            <td>${room.columns}</td>
            <td>${room.studentsPerBench}</td>
            <td>${room.capacity}</td>
            <td>${room.teacher || 'Not assigned'}</td>
            <td>
                <button onclick="window.Rooms.editRoom(${room.id})" class="btn-edit">Edit</button>
                <button onclick="window.Rooms.deleteRoom(${room.id})" class="btn-delete">Delete</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Edit room
function editRoom(id) {
    const rooms = window.Data.getData('rooms');
    const room = rooms.find(r => r.id === id);
    
    if (!room) {
        alert('Room not found');
        return;
    }
    
    // Create modal for editing
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <h3>Edit Room</h3>
            <form id="editRoomForm">
                <div class="form-group">
                    <label for="editRoomName">Room Name/Number</label>
                    <input type="text" id="editRoomName" value="${room.name}" required>
                </div>
                <div class="form-group">
                    <label for="editRoomRows">Number of Rows</label>
                    <input type="number" id="editRoomRows" value="${room.rows}" required min="1">
                </div>
                <div class="form-group">
                    <label for="editRoomColumns">Number of Columns</label>
                    <input type="number" id="editRoomColumns" value="${room.columns}" required min="1">
                </div>
                <div class="form-group">
                    <label for="editStudentsPerBench">Students Per Bench (max 4)</label>
                    <input type="number" id="editStudentsPerBench" value="${room.studentsPerBench}" required min="1" max="4">
                </div>
                <div class="form-group">
                    <label for="editTeacherName">Teacher In-charge</label>
                    <input type="text" id="editTeacherName" value="${room.teacher || ''}">
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn">Save Changes</button>
                    <button type="button" class="btn btn-cancel" onclick="window.Utils.closeModal()">Cancel</button>
                </div>
            </form>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Handle form submission
    document.getElementById('editRoomForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const rows = parseInt(document.getElementById('editRoomRows').value);
        const columns = parseInt(document.getElementById('editRoomColumns').value);
        const studentsPerBench = parseInt(document.getElementById('editStudentsPerBench').value);
        
        // Validate students per bench (max 4)
        if (studentsPerBench < 1 || studentsPerBench > 4) {
            alert('Students per bench must be between 1 and 4');
            return;
        }
        
        room.name = document.getElementById('editRoomName').value;
        room.rows = rows;
        room.columns = columns;
        room.studentsPerBench = studentsPerBench;
        room.capacity = rows * columns * studentsPerBench;
        room.teacher = document.getElementById('editTeacherName').value;
        
        window.Data.saveData('rooms', rooms);
        
        renderRoomsTable();
        
        // Reload dropdowns
        window.Dashboard.loadRoomsDropdown('visualRoom');
        
        window.Utils.closeModal();
    });
}

// Delete room
function deleteRoom(id) {
    if (confirm('Are you sure you want to delete this room?')) {
        let rooms = window.Data.getData('rooms');
        rooms = rooms.filter(r => r.id !== id);
        
        window.Data.saveData('rooms', rooms);
        
        renderRoomsTable();
        
        // Reload dropdowns
        window.Dashboard.loadRoomsDropdown('visualRoom');
    }
}

// Export functions
window.Rooms = {
    initRooms,
    addRoom,
    renderRoomsTable,
    editRoom,
    deleteRoom
};
