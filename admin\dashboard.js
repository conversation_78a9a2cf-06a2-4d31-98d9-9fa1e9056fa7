/**
 * Admin Dashboard Module
 * Handles the admin dashboard functionality
 */

// Initialize dashboard
function initDashboard() {
    // Check if user is logged in and is an admin
    if (!window.Auth.requireAdmin()) {
        return;
    }
    
    // Display admin name
    const adminNameElement = document.getElementById('adminName');
    const currentUser = window.Auth.getCurrentUser();
    
    if (adminNameElement && currentUser.name) {
        adminNameElement.textContent = currentUser.name;
    }
    
    // Load dropdowns
    loadExamsDropdown('seatingExam');
    loadExamsDropdown('visualExam');
    loadRoomsDropdown('visualRoom');
    loadCoursesDropdown('studentCourse');
    loadCourseCheckboxes();
    
    // Handle course selection toggle
    const courseSelection = document.getElementById('courseSelection');
    if (courseSelection) {
        courseSelection.addEventListener('change', function() {
            const numStudentsGroup = document.getElementById('numStudentsGroup');
            const courseListGroup = document.getElementById('courseListGroup');
            
            if (this.value === 'all') {
                numStudentsGroup.style.display = 'block';
                courseListGroup.style.display = 'none';
            } else {
                numStudentsGroup.style.display = 'none';
                courseListGroup.style.display = 'block';
            }
        });
    }
}

// Load exams dropdown
function loadExamsDropdown(elementId) {
    const dropdown = document.getElementById(elementId);
    if (!dropdown) return;
    
    const exams = window.Data.getData('exams');
    
    // Clear existing options except the first one
    dropdown.innerHTML = '<option value="">-- Select Exam --</option>';
    
    // Add exam options
    exams.forEach(exam => {
        const option = document.createElement('option');
        option.value = exam.id;
        option.textContent = `${exam.name} (${exam.date}, ${exam.time})`;
        dropdown.appendChild(option);
    });
}

// Load rooms dropdown
function loadRoomsDropdown(elementId) {
    const dropdown = document.getElementById(elementId);
    if (!dropdown) return;
    
    const rooms = window.Data.getData('rooms');
    
    // Clear existing options except the first one
    dropdown.innerHTML = '<option value="">-- Select Room --</option>';
    
    // Add room options
    rooms.forEach(room => {
        const option = document.createElement('option');
        option.value = room.id;
        option.textContent = `${room.name} (Capacity: ${room.capacity})`;
        dropdown.appendChild(option);
    });
}

// Load courses dropdown
function loadCoursesDropdown(elementId) {
    const dropdown = document.getElementById(elementId);
    if (!dropdown) return;
    
    const courses = window.Data.getData('courses');
    
    // Clear existing options except the first one
    dropdown.innerHTML = '<option value="">-- Select Course --</option>';
    
    // Add course options
    courses.forEach(course => {
        const option = document.createElement('option');
        option.value = course.id;
        option.textContent = course.name;
        dropdown.appendChild(option);
    });
}

// Load course checkboxes
function loadCourseCheckboxes() {
    const container = document.getElementById('courseCheckboxes');
    if (!container) return;
    
    const courses = window.Data.getData('courses');
    
    // Clear existing checkboxes
    container.innerHTML = '';
    
    // Add course checkboxes
    courses.forEach(course => {
        const div = document.createElement('div');
        div.className = 'checkbox-item';
        div.innerHTML = `
            <input type="checkbox" id="course-${course.id}" name="selectedCourses" value="${course.id}">
            <label for="course-${course.id}">${course.name}</label>
        `;
        container.appendChild(div);
    });
}

// Export functions
window.Dashboard = {
    initDashboard,
    loadExamsDropdown,
    loadRoomsDropdown,
    loadCoursesDropdown,
    loadCourseCheckboxes
};
