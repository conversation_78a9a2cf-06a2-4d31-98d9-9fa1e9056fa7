/**
 * Authentication Module
 * Handles user authentication, session management, and authorization
 * Uses localStorage for data storage
 */

// Current user cache
let currentUserCache = null;
let authChecked = false;

// Check if user is logged in
async function isLoggedIn() {
    if (!authChecked) {
        await checkAuth();
    }
    return currentUserCache !== null;
}

// Check if user is admin
async function isAdmin() {
    if (!authChecked) {
        await checkAuth();
    }
    return currentUserCache && (currentUserCache.role === 'admin' || currentUserCache.role === 'teacher');
}

// Get current user
async function getCurrentUser() {
    if (!authChecked) {
        await checkAuth();
    }
    return currentUserCache || {};
}

// Check authentication status
async function checkAuth() {
    // Always use localStorage for authentication
    return checkAuthFromLocalStorage();
}

// Check authentication from localStorage (for demo mode)
function checkAuthFromLocalStorage() {
    const storedUser = localStorage.getItem('currentUser');

    if (storedUser) {
        currentUserCache = JSON.parse(storedUser);
        authChecked = true;
        return true;
    } else {
        currentUserCache = null;
        authChecked = true;
        return false;
    }
}

// Login user
async function login(email, password) {
    // Always use localStorage for login
    return loginFromLocalStorage(email, password);
}

// Login from localStorage (for demo mode)
function loginFromLocalStorage(email, password) {
    // Get users from localStorage
    const storedUsers = localStorage.getItem('users');

    if (!storedUsers) {
        throw new Error('No users found. Please register first.');
    }

    const users = JSON.parse(storedUsers);
    const user = users.find(u => u.email === email && u.password === password);

    if (!user) {
        throw new Error('Invalid email or password');
    }

    // Create user data object
    const userData = {
        id: user.id || user.studentId || user.email,
        name: user.name,
        email: user.email,
        role: user.role
    };

    // Store in localStorage and cache
    localStorage.setItem('currentUser', JSON.stringify(userData));
    currentUserCache = userData;
    authChecked = true;

    return userData;
}

// Login admin
async function adminLogin(email, password) {
    // Always use localStorage for admin login
    return adminLoginFromLocalStorage(email, password);
}

// Admin login from localStorage (for demo mode)
function adminLoginFromLocalStorage(email, password) {
    // Check if credentials match admin credentials
    if (email !== '<EMAIL>' || password !== 'admin@1') {
        throw new Error('Invalid admin credentials');
    }

    // Create admin user data
    const userData = {
        id: 1,
        name: 'Administrator',
        email: '<EMAIL>',
        role: 'admin'
    };

    // Store in localStorage and cache
    localStorage.setItem('currentUser', JSON.stringify(userData));
    currentUserCache = userData;
    authChecked = true;

    return userData;
}

// Register user
async function register(userData) {
    // Always use localStorage for registration
    return registerToLocalStorage(userData);
}

// Register to localStorage (for demo mode)
function registerToLocalStorage(userData) {
    // Get existing users
    const storedUsers = localStorage.getItem('users');
    const users = storedUsers ? JSON.parse(storedUsers) : [];

    // Check if email already exists
    if (users.some(user => user.email === userData.email)) {
        throw new Error('Email already registered');
    }

    // Create new user
    const newUser = {
        id: Date.now(),
        name: userData.name,
        email: userData.email,
        password: userData.password,
        role: userData.role,
        studentId: userData.student_id || null
    };

    // Add to users array
    users.push(newUser);

    // Save to localStorage
    localStorage.setItem('users', JSON.stringify(users));

    // If role is student, also add to students array
    if (userData.role === 'student') {
        const storedStudents = localStorage.getItem('students');
        const students = storedStudents ? JSON.parse(storedStudents) : [];

        students.push({
            id: userData.student_id,
            name: userData.name,
            email: userData.email,
            course_id: 1 // Default course ID
        });

        localStorage.setItem('students', JSON.stringify(students));
    }

    return { success: true, message: 'Registration successful' };
}

// Logout user
async function logout() {
    // Clear localStorage
    localStorage.removeItem('currentUser');
    currentUserCache = null;
    authChecked = false;
    window.location.href = '../index.html';
}

// Redirect if not logged in
async function requireLogin() {
    const loggedIn = await isLoggedIn();

    if (!loggedIn) {
        alert('You must be logged in to access this page.');
        window.location.href = '../index.html';
        return false;
    }

    return true;
}

// Redirect if not admin
async function requireAdmin() {
    const admin = await isAdmin();

    if (!admin) {
        alert('You must be logged in as an admin to access this page.');
        window.location.href = '../index.html';
        return false;
    }

    return true;
}

// Check if email is authorized
async function isAuthorizedEmail(email, role) {
    try {
        const authorizedUsers = await window.Data.getData(
            role === 'student' ? 'authorizedStudents' : 'authorizedTeachers'
        );

        return authorizedUsers.some(user => user.email === email);
    } catch (error) {
        console.error('Error checking authorized email:', error);
        return false;
    }
}

// Export functions
window.Auth = {
    isLoggedIn,
    isAdmin,
    getCurrentUser,
    checkAuth,
    login,
    adminLogin,
    register,
    logout,
    requireLogin,
    requireAdmin,
    isAuthorizedEmail
};
