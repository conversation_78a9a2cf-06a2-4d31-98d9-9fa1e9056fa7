/**
 * Demo Data Script
 * This script adds demo data to localStorage for the demo mode
 */

// Sample courses
const courses = [
    { id: 1, name: 'Computer Science' },
    { id: 2, name: 'Artificial Intelligence' },
    { id: 3, name: 'Physics' },
    { id: 4, name: 'Chemistry' },
    { id: 5, name: 'Biology' }
];

// Sample rooms
const rooms = [
    { id: 1, name: 'Room 101', rows: 5, columns: 5, students_per_bench: 4, capacity: 100, teacher: '<PERSON>' },
    { id: 2, name: 'Room 102', rows: 4, columns: 4, students_per_bench: 4, capacity: 64, teacher: '<PERSON>' },
    { id: 3, name: 'Room 103', rows: 6, columns: 6, students_per_bench: 2, capacity: 72, teacher: '<PERSON>' }
];

// Sample exams
const exams = [
    { id: 1, name: 'Midterm Exam', date: '2025-06-15', time: '09:00:00', duration: 120 },
    { id: 2, name: 'Final Exam', date: '2025-07-30', time: '10:00:00', duration: 180 },
    { id: 3, name: 'Quiz 1', date: '2025-06-01', time: '14:00:00', duration: 60 }
];

// Sample students
const students = [
    { id: 'STUD001', name: 'Test Student', email: '<EMAIL>', course_id: 1 },
    { id: 'STUD002', name: 'John Doe', email: '<EMAIL>', course_id: 1 },
    { id: 'STUD003', name: 'Jane Smith', email: '<EMAIL>', course_id: 2 },
    { id: 'STUD004', name: 'Robert Brown', email: '<EMAIL>', course_id: 1 },
    { id: 'STUD005', name: 'Emily Wilson', email: '<EMAIL>', course_id: 3 },
    { id: 'STUD006', name: 'Michael Johnson', email: '<EMAIL>', course_id: 2 },
    { id: 'STUD007', name: 'Sarah Davis', email: '<EMAIL>', course_id: 4 },
    { id: 'STUD008', name: 'David Miller', email: '<EMAIL>', course_id: 5 },
    { id: 'STUD009', name: 'Lisa Garcia', email: '<EMAIL>', course_id: 3 },
    { id: 'STUD010', name: 'James Wilson', email: '<EMAIL>', course_id: 1 }
];

// Sample users
const users = [
    {
        id: 1,
        name: 'Administrator',
        email: '<EMAIL>',
        password: 'admin@1',
        role: 'admin'
    },
    {
        id: 2,
        name: 'Test Student',
        email: '<EMAIL>',
        password: '123',
        role: 'student',
        studentId: 'STUD001'
    }
];

// Sample authorized students
const authorizedStudents = students.map(student => ({
    id: student.id,
    name: student.name,
    email: student.email,
    user_id: student.id,
    type: 'student',
    course_id: student.course_id,
    registered: student.id === 'STUD001' // Only the test student is registered
}));

// Sample authorized teachers
const authorizedTeachers = [
    { id: 'TEACH001', name: 'John Smith', email: '<EMAIL>', user_id: 'TEACH001', type: 'teacher', department: 'Computer Science', registered: false },
    { id: 'TEACH002', name: 'Jane Doe', email: '<EMAIL>', user_id: 'TEACH002', type: 'teacher', department: 'Physics', registered: false },
    { id: 'TEACH003', name: 'Robert Johnson', email: '<EMAIL>', user_id: 'TEACH003', type: 'teacher', department: 'Chemistry', registered: false }
];

// Save data to localStorage
function saveDataToLocalStorage() {
    localStorage.setItem('courses', JSON.stringify(courses));
    localStorage.setItem('rooms', JSON.stringify(rooms));
    localStorage.setItem('exams', JSON.stringify(exams));
    localStorage.setItem('students', JSON.stringify(students));
    localStorage.setItem('users', JSON.stringify(users));
    localStorage.setItem('authorizedStudents', JSON.stringify(authorizedStudents));
    localStorage.setItem('authorizedTeachers', JSON.stringify(authorizedTeachers));
    
    // Set demo mode flag
    localStorage.setItem('demoInitialized', 'true');
    
    // Show success message
    const resultDiv = document.getElementById('result');
    if (resultDiv) {
        resultDiv.innerHTML = `
            <div class="success">
                <h3>Demo Data Added Successfully!</h3>
                <p>The following data has been added to localStorage:</p>
                <ul>
                    <li>5 Courses</li>
                    <li>3 Rooms</li>
                    <li>3 Exams</li>
                    <li>10 Students</li>
                    <li>2 Users (Admin and Test Student)</li>
                </ul>
                <p><strong>Student Login:</strong></p>
                <ul>
                    <li>Email: <EMAIL></li>
                    <li>Password: 123</li>
                </ul>
                <p><strong>Admin Login:</strong></p>
                <ul>
                    <li>Email: <EMAIL></li>
                    <li>Password: admin@1</li>
                </ul>
                <p><a href="index.html" class="btn">Go to Login Page</a></p>
            </div>
        `;
    }
}

// Initialize when the page loads
document.addEventListener('DOMContentLoaded', function() {
    const addDataButton = document.getElementById('addDataButton');
    if (addDataButton) {
        addDataButton.addEventListener('click', saveDataToLocalStorage);
    }
});
