/**
 * Authorized Users Module
 * Handles authorized users management functionality
 */

// Initialize authorized users module
function initAuthorizedUsers() {
    // Add event listener for authorized users form
    const authorizedUsersForm = document.getElementById('authorizedUsersForm');
    if (authorizedUsersForm) {
        authorizedUsersForm.addEventListener('submit', uploadAuthorizedUsers);
    }
    
    // Add event listener for user type selection
    const userTypeSelect = document.getElementById('userType');
    if (userTypeSelect) {
        userTypeSelect.addEventListener('change', updateTemplateInfo);
    }
    
    // Initialize template info
    updateTemplateInfo();
    
    // Load authorized users from localStorage
    loadAuthorizedUsers();
    
    // Render authorized users tables
    renderAuthorizedUsersTable();
}

// Load authorized users from localStorage
function loadAuthorizedUsers() {
    const authorizedStudents = window.Data.getData('authorizedStudents');
    const authorizedTeachers = window.Data.getData('authorizedTeachers');
    
    // Update registration status based on existing users
    const users = window.Data.getData('users');
    
    authorizedStudents.forEach(student => {
        student.registered = users.some(user => user.email === student.email);
    });
    
    authorizedTeachers.forEach(teacher => {
        teacher.registered = users.some(user => user.email === teacher.email);
    });
    
    window.Data.saveData('authorizedStudents', authorizedStudents);
    window.Data.saveData('authorizedTeachers', authorizedTeachers);
}

// Update template info based on selected user type
function updateTemplateInfo() {
    const userType = document.getElementById('userType').value;
    const templateInfo = document.getElementById('templateInfo');
    
    if (templateInfo) {
        if (userType === 'student') {
            templateInfo.innerHTML = '<p><strong>Student Template Format:</strong> Serial No, Name, Email, USN/Roll Number, Course</p>';
        } else {
            templateInfo.innerHTML = '<p><strong>Teacher Template Format:</strong> Serial No, Name, Email, ID, Department</p>';
        }
    }
}

// Upload authorized users
function uploadAuthorizedUsers(e) {
    e.preventDefault();
    
    const userType = document.getElementById('userType').value;
    const fileInput = document.getElementById('authorizedFile');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('Please select a file to upload');
        return;
    }
    
    const reader = new FileReader();
    
    reader.onload = function(e) {
        try {
            let newUsers = [];
            
            if (file.name.endsWith('.csv')) {
                // Parse CSV
                const csvData = e.target.result;
                const lines = csvData.split('\n');
                
                // Skip header row
                for (let i = 1; i < lines.length; i++) {
                    if (lines[i].trim() === '') continue; // Skip empty lines
                    
                    const values = lines[i].split(',');
                    
                    if (values.length >= 4) {
                        if (userType === 'student') {
                            // Format for students: Serial No, Name, Email, USN/Roll Number, Course
                            const [, name, email, id, course] = values;
                            
                            if (name && email && id) {
                                const courseId = parseInt(course) || 1;
                                newUsers.push({ id, name, email, course: courseId, registered: false });
                            }
                        } else {
                            // Format for teachers: Serial No, Name, Email, ID, Department
                            const [, name, email, id, department] = values;
                            
                            if (name && email && id) {
                                newUsers.push({ id, name, email, department: department || 'General', registered: false });
                            }
                        }
                    }
                }
            } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                // Parse Excel
                const workbook = XLSX.read(e.target.result, { type: 'array' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                
                // Skip header row
                for (let i = 1; i < data.length; i++) {
                    if (!data[i] || data[i].length === 0) continue; // Skip empty rows
                    
                    if (data[i].length >= 4) {
                        if (userType === 'student') {
                            // Format for students: Serial No, Name, Email, USN/Roll Number, Course
                            const [, name, email, id, course] = data[i];
                            
                            if (name && email && id) {
                                const courseId = parseInt(course) || 1;
                                newUsers.push({ id, name, email, course: courseId, registered: false });
                            }
                        } else {
                            // Format for teachers: Serial No, Name, Email, ID, Department
                            const [, name, email, id, department] = data[i];
                            
                            if (name && email && id) {
                                newUsers.push({ id, name, email, department: department || 'General', registered: false });
                            }
                        }
                    }
                }
            } else {
                alert('Unsupported file format. Please upload a CSV or Excel file.');
                return;
            }
            
            if (newUsers.length === 0) {
                alert('No valid users found in the file.');
                return;
            }
            
            // Check for existing users and update the registered status
            const users = window.Data.getData('users');
            
            if (userType === 'student') {
                const authorizedStudents = window.Data.getData('authorizedStudents');
                
                // Update registered status for new users
                newUsers.forEach(newUser => {
                    newUser.registered = users.some(user => user.email === newUser.email);
                    
                    // Check if user already exists in authorized list
                    const existingAuthorized = authorizedStudents.find(user => user.email === newUser.email);
                    if (existingAuthorized) {
                        // Update existing user
                        existingAuthorized.name = newUser.name;
                        existingAuthorized.id = newUser.id;
                        existingAuthorized.course = newUser.course;
                        existingAuthorized.registered = newUser.registered;
                    } else {
                        // Add new user
                        authorizedStudents.push(newUser);
                    }
                });
                
                window.Data.saveData('authorizedStudents', authorizedStudents);
            } else {
                const authorizedTeachers = window.Data.getData('authorizedTeachers');
                
                // Update registered status for new users
                newUsers.forEach(newUser => {
                    newUser.registered = users.some(user => user.email === newUser.email);
                    
                    // Check if user already exists in authorized list
                    const existingAuthorized = authorizedTeachers.find(user => user.email === newUser.email);
                    if (existingAuthorized) {
                        // Update existing user
                        existingAuthorized.name = newUser.name;
                        existingAuthorized.id = newUser.id;
                        existingAuthorized.department = newUser.department;
                        existingAuthorized.registered = newUser.registered;
                    } else {
                        // Add new user
                        authorizedTeachers.push(newUser);
                    }
                });
                
                window.Data.saveData('authorizedTeachers', authorizedTeachers);
            }
            
            // Render tables
            renderAuthorizedUsersTable();
            
            // Reset form
            document.getElementById('authorizedUsersForm').reset();
            
            alert(`Successfully uploaded ${newUsers.length} ${userType}s.`);
        } catch (error) {
            console.error('Error parsing file:', error);
            alert('Error parsing file. Please check the file format and try again.');
        }
    };
    
    if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        reader.readAsArrayBuffer(file);
    } else {
        reader.readAsText(file);
    }
}

// Render authorized users tables
function renderAuthorizedUsersTable() {
    const studentsTable = document.getElementById('authorizedStudentsTable');
    const teachersTable = document.getElementById('authorizedTeachersTable');
    
    if (studentsTable) {
        const tbody = studentsTable.querySelector('tbody');
        tbody.innerHTML = '';
        
        const authorizedStudents = window.Data.getData('authorizedStudents');
        const courses = window.Data.getData('courses');
        
        authorizedStudents.forEach(student => {
            const course = courses.find(c => c.id === student.course);
            const courseName = course ? course.name : 'Unknown';
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${student.name}</td>
                <td>${student.email}</td>
                <td>${student.id}</td>
                <td>${courseName}</td>
                <td>${student.registered ? '<span class="status-registered">Yes</span>' : '<span class="status-pending">No</span>'}</td>
                <td>
                    <button onclick="window.AuthorizedUsers.removeAuthorizedUser('student', '${student.email}')" class="btn-delete">Remove</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }
    
    if (teachersTable) {
        const tbody = teachersTable.querySelector('tbody');
        tbody.innerHTML = '';
        
        const authorizedTeachers = window.Data.getData('authorizedTeachers');
        
        authorizedTeachers.forEach(teacher => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${teacher.name}</td>
                <td>${teacher.email}</td>
                <td>${teacher.department}</td>
                <td>${teacher.registered ? '<span class="status-registered">Yes</span>' : '<span class="status-pending">No</span>'}</td>
                <td>
                    <button onclick="window.AuthorizedUsers.removeAuthorizedUser('teacher', '${teacher.email}')" class="btn-delete">Remove</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }
}

// Remove authorized user
function removeAuthorizedUser(type, email) {
    if (confirm(`Are you sure you want to remove this ${type} from the authorized list?`)) {
        if (type === 'student') {
            let authorizedStudents = window.Data.getData('authorizedStudents');
            authorizedStudents = authorizedStudents.filter(user => user.email !== email);
            window.Data.saveData('authorizedStudents', authorizedStudents);
        } else {
            let authorizedTeachers = window.Data.getData('authorizedTeachers');
            authorizedTeachers = authorizedTeachers.filter(user => user.email !== email);
            window.Data.saveData('authorizedTeachers', authorizedTeachers);
        }
        
        // Render tables
        renderAuthorizedUsersTable();
    }
}

// Export functions
window.AuthorizedUsers = {
    initAuthorizedUsers,
    loadAuthorizedUsers,
    updateTemplateInfo,
    uploadAuthorizedUsers,
    renderAuthorizedUsersTable,
    removeAuthorizedUser
};
