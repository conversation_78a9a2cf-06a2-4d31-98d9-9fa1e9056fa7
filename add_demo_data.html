<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Demo Data - Exam Seating</title>
    <link rel="stylesheet" href="css/main.css">
    <style>
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .btn {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Add Demo Data</h1>
        <p>This page will add demo data to the application, including:</p>
        <ul>
            <li>Sample courses</li>
            <li>Sample rooms</li>
            <li>Sample exams</li>
            <li>Sample students</li>
            <li>A student user account (<EMAIL> / 123)</li>
        </ul>
        <p>Click the button below to add the demo data:</p>
        <button id="addDataButton" class="btn">Add Demo Data</button>
        
        <div id="result"></div>
    </div>
    
    <script src="add_demo_data.js"></script>
</body>
</html>
