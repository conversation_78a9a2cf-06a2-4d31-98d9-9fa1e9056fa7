/**
 * Login Module
 * Handles user login functionality
 */

// Initialize login functionality
function initLogin() {
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleUserLogin);
    }

    const adminLoginForm = document.getElementById('adminLoginForm');
    if (adminLoginForm) {
        adminLoginForm.addEventListener('submit', handleAdminLogin);
    }

    // Check if user is already logged in
    window.Auth.checkAuth().then(isAuthenticated => {
        if (isAuthenticated) {
            window.Auth.getCurrentUser().then(user => {
                if (user.role === 'admin' || user.role === 'teacher') {
                    window.location.href = 'admin/dashboard.html';
                } else {
                    window.location.href = 'user/dashboard.html';
                }
            });
        }
    });
}

// Handle user login
async function handleUserLogin(e) {
    e.preventDefault();

    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;

    if (!email || !password) {
        alert('Please enter both email and password.');
        return;
    }

    try {
        // Show loading indicator
        const submitButton = e.target.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        submitButton.textContent = 'Logging in...';
        submitButton.disabled = true;

        // Attempt login
        const user = await window.Auth.login(email, password);

        // Redirect based on role
        if (user.role === 'admin' || user.role === 'teacher') {
            window.location.href = 'admin/dashboard.html';
        } else {
            window.location.href = 'user/dashboard.html';
        }
    } catch (error) {
        alert('Login failed: ' + (error.message || 'Invalid email or password. Please try again.'));

        // Reset button
        const submitButton = e.target.querySelector('button[type="submit"]');
        submitButton.textContent = 'Login';
        submitButton.disabled = false;
    }
}

// Handle admin login
async function handleAdminLogin(e) {
    e.preventDefault();

    // Get admin email and password
    const adminEmail = document.getElementById('adminEmail').value.trim();
    const adminPassword = document.getElementById('adminPassword').value;

    if (!adminEmail || !adminPassword) {
        alert('Please enter both email and password.');
        return;
    }

    try {
        // Show loading indicator
        const submitButton = e.target.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        submitButton.textContent = 'Logging in...';
        submitButton.disabled = true;

        // Attempt admin login
        const user = await window.Auth.adminLogin(adminEmail, adminPassword);

        // Redirect to admin dashboard
        window.location.href = 'admin/dashboard.html';
    } catch (error) {
        alert('Admin login failed: ' + (error.message || 'Invalid credentials. Please try again.'));

        // Reset button
        const submitButton = e.target.querySelector('button[type="submit"]');
        submitButton.textContent = 'Login';
        submitButton.disabled = false;
    }
}

// Export functions
window.Login = {
    initLogin,
    handleUserLogin,
    handleAdminLogin
};
