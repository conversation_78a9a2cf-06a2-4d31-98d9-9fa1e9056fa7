<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Dashboard - Exam Seating</title>
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="user.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Student Dashboard</h1>
            <button onclick="logout()" class="btn-logout">Logout</button>
        </header>

        <div class="dashboard">
            <div class="user-info">
                <h2>Welcome, <span id="studentName">Student</span></h2>
                <p>ID: <span id="studentId">12345</span></p>
            </div>

            <div class="seating-info">
                <h3>Your Exam Seating Arrangements</h3>
                <table id="seatingTable">
                    <thead>
                        <tr>
                            <th>Exam</th>
                            <th>Date</th>
                            <th>Time</th>
                            <th>Room</th>
                            <th>Seat No.</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Seating data will be loaded here -->
                    </tbody>
                </table>
            </div>

            <div class="seat-visualization">
                <h3>Seat Visualization</h3>
                <div class="form-group">
                    <label for="visualExam">Select Exam</label>
                    <select id="visualExam" onchange="loadStudentVisualization()">
                        <option value="">-- Select Exam --</option>
                        <!-- Exams will be loaded here -->
                    </select>
                </div>
                <div id="roomVisualization" class="room-visual">
                    <!-- Room visualization will be rendered here -->
                    <p>Select an exam to view your seat.</p>
                </div>
            </div>
        </div>
    </div>
    <!-- External libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- Common modules -->
    <script src="../common/utils.js"></script>
    <script src="../common/server.js"></script>
    <script src="../common/data.js"></script>
    <script src="../common/pdf-export.js"></script>
    <script src="../common/excel-export.js"></script>
    <script src="../common/visualization.js"></script>

    <!-- Authentication module -->
    <script src="../auth/auth.js"></script>

    <!-- User module -->
    <script src="user.js"></script>
</body>
</html>
