// Tab functionality
function showTab(tabId) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // Deactivate all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab content and activate button
    document.getElementById(tabId).classList.add('active');
    document.querySelector(`button[onclick="showTab('${tabId}')"]`).classList.add('active');
}

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // User Login form submission
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            console.log('Login attempt:', email, password);

            // Check if user exists in localStorage
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            const user = users.find(u => u.email === email && u.password === password);

            if (user) {
                // Store current user info
                localStorage.setItem('currentUser', JSON.stringify({
                    id: user.studentId || user.email,
                    name: user.name,
                    email: user.email,
                    role: user.role
                }));

                // Redirect based on role
                if (user.role === 'teacher') {
                    window.location.href = 'admin/dashboard.html';
                } else {
                    window.location.href = 'user/dashboard.html';
                }
            } else {
                alert('Invalid email or password. Please try again.');
            }
        });
    } else {
        console.error('Login form not found');
    }

    // Admin Login form submission
    const adminLoginForm = document.getElementById('adminLoginForm');
    if (adminLoginForm) {
        adminLoginForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get admin email and password
            const adminEmail = document.getElementById('adminEmail').value.trim();
            const adminPassword = document.getElementById('adminPassword').value;

            if (!adminEmail || !adminPassword) {
                alert('Please enter both email and password.');
                return;
            }

            // Check if credentials match
            if (adminEmail === '<EMAIL>' && adminPassword === 'admin@1') {
                // Store admin info in localStorage
                localStorage.setItem('currentUser', JSON.stringify({
                    email: adminEmail,
                    role: 'admin',
                    name: 'Administrator'
                }));

                // Redirect to admin dashboard
                window.location.href = 'admin/dashboard.html';
            } else {
                alert('Invalid admin credentials. Please try again.');
            }
        });
    } else {
        console.error('Admin login form not found');
    }

    // Registration form submission
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        // Show/hide student fields based on role selection
        const roleSelect = document.getElementById('registerRole');
        const studentFields = document.querySelector('.student-fields');

        roleSelect.addEventListener('change', function() {
            if (this.value === 'student') {
                studentFields.style.display = 'block';
            } else {
                studentFields.style.display = 'none';
            }
        });

        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('registerName').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('registerConfirmPassword').value;
            const role = document.getElementById('registerRole').value;
            const studentId = document.getElementById('registerStudentId').value;

            // Validate passwords match
            if (password !== confirmPassword) {
                alert('Passwords do not match!');
                return;
            }

            // Validate student ID if role is student
            if (role === 'student' && !studentId) {
                alert('Student ID is required for student registration.');
                return;
            }

            // Get existing users or create empty array
            const users = JSON.parse(localStorage.getItem('users') || '[]');

            // Check if email already exists
            if (users.some(user => user.email === email)) {
                alert('Email already registered. Please use a different email.');
                return;
            }

            // Check if email is authorized
            const authorizedStudents = JSON.parse(localStorage.getItem('authorizedStudents') || '[]');
            const authorizedTeachers = JSON.parse(localStorage.getItem('authorizedTeachers') || '[]');

            let isAuthorized = false;

            if (role === 'student') {
                isAuthorized = authorizedStudents.some(student => student.email === email);
            } else {
                isAuthorized = authorizedTeachers.some(teacher => teacher.email === email);
            }

            if (!isAuthorized) {
                alert('This email is not authorized to register. Please contact the administrator.');
                return;
            }

            // Add new user
            const newUser = {
                name,
                email,
                password,
                role,
                studentId: role === 'student' ? studentId : null
            };

            users.push(newUser);

            // Update authorized user status
            if (role === 'student') {
                const authorizedStudent = authorizedStudents.find(student => student.email === email);
                if (authorizedStudent) {
                    authorizedStudent.registered = true;
                    localStorage.setItem('authorizedStudents', JSON.stringify(authorizedStudents));
                }
            } else {
                const authorizedTeacher = authorizedTeachers.find(teacher => teacher.email === email);
                if (authorizedTeacher) {
                    authorizedTeacher.registered = true;
                    localStorage.setItem('authorizedTeachers', JSON.stringify(authorizedTeachers));
                }
            }

            // Save to localStorage
            localStorage.setItem('users', JSON.stringify(users));

            // If role is student, also add to students array in admin
            if (role === 'student') {
                // Get existing students or create empty array
                const students = JSON.parse(localStorage.getItem('students') || '[]');

                // Add new student
                students.push({
                    id: studentId,
                    name,
                    email
                });

                // Save to localStorage
                localStorage.setItem('students', JSON.stringify(students));
            }

            alert('Registration successful! You can now log in.');

            // Reset form and switch to login tab
            registerForm.reset();
            showTab('login');
        });
    } else {
        console.error('Register form not found');
    }
});
