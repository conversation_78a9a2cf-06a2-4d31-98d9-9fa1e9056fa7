<?php
// Start session
session_start();

// Output session information
echo "<h2>Session Information</h2>";
echo "<pre>";
echo "Session ID: " . session_id() . "\n";
echo "Session Status: " . session_status() . "\n";
echo "Session Save Path: " . session_save_path() . "\n";
echo "</pre>";

// Check if session variables are set
echo "<h2>Session Variables</h2>";
echo "<pre>";
if (isset($_SESSION) && !empty($_SESSION)) {
    foreach ($_SESSION as $key => $value) {
        echo "$key: " . (is_array($value) ? json_encode($value) : $value) . "\n";
    }
} else {
    echo "No session variables set.\n";
}
echo "</pre>";

// Set a test session variable
$_SESSION['test'] = 'Session is working!';
echo "<p>Set test session variable. Refresh to see it.</p>";

// Output PHP info about sessions
echo "<h2>PHP Session Configuration</h2>";
echo "<pre>";
$sessionSettings = [
    'session.save_handler',
    'session.save_path',
    'session.use_cookies',
    'session.use_only_cookies',
    'session.name',
    'session.auto_start',
    'session.cookie_lifetime',
    'session.cookie_path',
    'session.cookie_domain',
    'session.cookie_httponly',
    'session.serialize_handler',
    'session.gc_probability',
    'session.gc_divisor',
    'session.gc_maxlifetime',
    'session.referer_check',
    'session.cache_limiter',
    'session.cache_expire',
    'session.use_trans_sid',
    'session.sid_length',
    'session.trans_sid_tags',
    'session.sid_bits_per_character'
];

foreach ($sessionSettings as $setting) {
    echo "$setting: " . ini_get($setting) . "\n";
}
echo "</pre>";
?>
