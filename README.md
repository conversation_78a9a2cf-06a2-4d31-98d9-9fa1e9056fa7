# Exam Seating Arrangement System

A comprehensive system for managing exam seating arrangements with various seating strategies, multiple room support, and user management.

## Features

- User authentication with role-based access control
- Authorized user registration system
- Exam management
- Room management
- Student management
- Course management
- Multiple seating arrangement strategies:
  - Random
  - Zigzag
  - Set-wise (A, B, C, D)
  - Front-to-back
  - Back-to-front
- Visualization of seating arrangements
- Export to PDF and Excel
- Multiple courses in a single room
- Automatic room allocation when capacity is exceeded

## Setup Instructions

### Prerequisites

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- Modern web browser

### Database Setup

1. Create a new MySQL database named `exam_seating`
2. Update database credentials in `api/config.php` if needed:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'exam_seating');
   define('DB_USER', 'root');
   define('DB_PASS', '');
   ```

### Installation

1. Clone the repository to your web server's document root
2. Navigate to `http://your-server/exam_seating/api/init.php` to initialize the database
3. Access the application at `http://your-server/exam_seating/`

### Default Admin Credentials

- Email: <EMAIL>
- Password: admin@1

## Project Structure

- `/api` - PHP backend API endpoints
- `/auth` - Authentication modules
- `/common` - Common utilities and data management
- `/css` - Stylesheets
- `/admin` - Admin dashboard modules
- `/user` - User dashboard modules
- `/templates` - CSV/Excel templates

## API Endpoints

- `/api/auth.php` - Authentication API
- `/api/students.php` - Students API
- `/api/exams.php` - Exams API
- `/api/rooms.php` - Rooms API
- `/api/courses.php` - Courses API
- `/api/authorized_users.php` - Authorized Users API
- `/api/seating.php` - Seating Arrangements API

## Usage

1. **Admin Login**: Use the admin credentials to access the admin dashboard
2. **Upload Authorized Users**: Upload a list of authorized students and teachers
3. **Create Courses**: Add courses for students
4. **Create Rooms**: Define exam rooms with their seating capacity
5. **Create Exams**: Schedule exams
6. **Add Students**: Add students manually or upload via CSV/Excel
7. **Generate Seating Arrangements**: Create seating arrangements for exams
8. **Visualize Seating**: View and export seating arrangements

## License

This project is licensed under the MIT License - see the LICENSE file for details.
