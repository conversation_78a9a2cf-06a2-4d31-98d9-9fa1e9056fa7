/**
 * PDF Export Module
 * Handles exporting seating arrangements to PDF
 */

// Generate PDF for seating arrangement
async function generateSeatingPDF(arrangementId) {
    try {
        // Get seating arrangement data
        const arrangements = await window.Data.getData('seatingArrangements');
        const arrangement = arrangements.find(a => a.id === arrangementId);
        
        if (!arrangement) {
            throw new Error('Seating arrangement not found');
        }
        
        // Get exam data
        const exams = await window.Data.getData('exams');
        const exam = exams.find(e => e.id === arrangement.exam_id);
        
        if (!exam) {
            throw new Error('Exam not found');
        }
        
        // Get room data
        const rooms = await window.Data.getData('rooms');
        const room = rooms.find(r => r.id === arrangement.room_id);
        
        if (!room) {
            throw new Error('Room not found');
        }
        
        // Get course data
        const courses = await window.Data.getData('courses');
        const course = courses.find(c => c.id === arrangement.course_id);
        
        // Create PDF document
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });
        
        // Add title
        doc.setFontSize(18);
        doc.setFont('helvetica', 'bold');
        doc.text('Exam Seating Arrangement', 105, 15, { align: 'center' });
        
        // Add exam details
        doc.setFontSize(12);
        doc.setFont('helvetica', 'normal');
        doc.text(`Exam: ${exam.name}`, 20, 30);
        doc.text(`Date: ${formatDate(exam.date)}`, 20, 37);
        doc.text(`Time: ${formatTime(exam.time)}`, 20, 44);
        doc.text(`Duration: ${exam.duration} minutes`, 20, 51);
        
        // Add room details
        doc.text(`Room: ${room.name}`, 120, 30);
        doc.text(`Capacity: ${room.capacity}`, 120, 37);
        doc.text(`Teacher: ${room.teacher}`, 120, 44);
        
        // Add course details if available
        if (course) {
            doc.text(`Course: ${course.name}`, 120, 51);
        }
        
        // Add arrangement details
        doc.text(`Arrangement Type: ${formatArrangementType(arrangement.type)}`, 20, 58);
        doc.text(`Total Students: ${arrangement.seats.length}`, 120, 58);
        
        // Add seating chart
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.text('Seating Chart', 105, 70, { align: 'center' });
        
        // Draw room layout
        drawRoomLayout(doc, room, arrangement, 20, 80);
        
        // Add student list
        addStudentList(doc, arrangement);
        
        // Save the PDF
        doc.save(`seating_arrangement_${arrangementId}.pdf`);
        
        return true;
    } catch (error) {
        console.error('Error generating PDF:', error);
        alert('Failed to generate PDF: ' + error.message);
        return false;
    }
}

// Draw room layout in PDF
function drawRoomLayout(doc, room, arrangement, x, y) {
    const cellWidth = 25;
    const cellHeight = 15;
    const maxWidth = 170; // Maximum width available on A4 page
    
    // Calculate scaling factor if room is too wide
    const totalWidth = room.columns * cellWidth;
    const scale = totalWidth > maxWidth ? maxWidth / totalWidth : 1;
    
    // Adjust cell dimensions based on scale
    const scaledCellWidth = cellWidth * scale;
    const scaledCellHeight = cellHeight * scale;
    
    // Draw room border
    const roomWidth = room.columns * scaledCellWidth;
    const roomHeight = room.rows * scaledCellHeight;
    doc.setDrawColor(0);
    doc.setLineWidth(0.5);
    doc.rect(x, y, roomWidth, roomHeight);
    
    // Draw benches and assign students
    for (let row = 0; row < room.rows; row++) {
        for (let col = 0; col < room.columns; col++) {
            const benchX = x + col * scaledCellWidth;
            const benchY = y + row * scaledCellHeight;
            
            // Draw bench
            doc.setDrawColor(100);
            doc.setLineWidth(0.3);
            doc.rect(benchX, benchY, scaledCellWidth, scaledCellHeight);
            
            // Find students assigned to this bench
            const benchStudents = arrangement.seats.filter(seat => 
                seat.row === row && seat.column === col
            );
            
            if (benchStudents.length > 0) {
                // Display student info
                doc.setFontSize(6 * scale);
                doc.setFont('helvetica', 'normal');
                
                benchStudents.forEach((student, index) => {
                    const studentY = benchY + 3 + (index * 3 * scale);
                    
                    // Truncate long names
                    let displayName = student.student_name;
                    if (displayName.length > 15) {
                        displayName = displayName.substring(0, 13) + '...';
                    }
                    
                    // Add student ID and name
                    doc.text(`${student.student_id}: ${displayName}`, benchX + 2, studentY);
                    
                    // Add set number if available
                    if (student.set) {
                        doc.setFont('helvetica', 'bold');
                        doc.text(`Set ${student.set}`, benchX + scaledCellWidth - 8, studentY);
                        doc.setFont('helvetica', 'normal');
                    }
                });
            }
        }
    }
    
    // Add legend
    const legendY = y + roomHeight + 10;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'bold');
    doc.text('Legend:', x, legendY);
    doc.setFont('helvetica', 'normal');
    doc.text('Each rectangle represents a bench.', x + 20, legendY);
    doc.text('Student format: ID: Name (Set)', x + 20, legendY + 7);
}

// Add student list to PDF
function addStudentList(doc, arrangement) {
    // Add a new page for student list
    doc.addPage();
    
    // Add title
    doc.setFontSize(16);
    doc.setFont('helvetica', 'bold');
    doc.text('Student List', 105, 15, { align: 'center' });
    
    // Sort students by ID
    const sortedStudents = [...arrangement.seats].sort((a, b) => 
        a.student_id.localeCompare(b.student_id)
    );
    
    // Define table columns
    const columns = [
        { header: 'Student ID', width: 30 },
        { header: 'Name', width: 60 },
        { header: 'Row', width: 20 },
        { header: 'Column', width: 20 },
        { header: 'Set', width: 20 }
    ];
    
    // Calculate total width
    const totalWidth = columns.reduce((sum, col) => sum + col.width, 0);
    const startX = (210 - totalWidth) / 2; // Center table on A4 page
    
    // Draw table header
    let currentY = 25;
    let currentX = startX;
    
    doc.setFillColor(240, 240, 240);
    doc.rect(startX, currentY - 5, totalWidth, 7, 'F');
    
    doc.setFontSize(10);
    doc.setFont('helvetica', 'bold');
    
    columns.forEach(column => {
        doc.text(column.header, currentX + 2, currentY);
        currentX += column.width;
    });
    
    // Draw table rows
    doc.setFont('helvetica', 'normal');
    currentY += 7;
    
    sortedStudents.forEach((student, index) => {
        // Add new page if needed
        if (currentY > 280) {
            doc.addPage();
            currentY = 25;
            
            // Redraw header on new page
            currentX = startX;
            doc.setFillColor(240, 240, 240);
            doc.rect(startX, currentY - 5, totalWidth, 7, 'F');
            
            doc.setFontSize(10);
            doc.setFont('helvetica', 'bold');
            
            columns.forEach(column => {
                doc.text(column.header, currentX + 2, currentY);
                currentX += column.width;
            });
            
            doc.setFont('helvetica', 'normal');
            currentY += 7;
        }
        
        // Alternate row colors
        if (index % 2 === 0) {
            doc.setFillColor(250, 250, 250);
            doc.rect(startX, currentY - 5, totalWidth, 7, 'F');
        }
        
        // Draw row data
        currentX = startX;
        
        doc.text(student.student_id, currentX + 2, currentY);
        currentX += columns[0].width;
        
        doc.text(student.student_name, currentX + 2, currentY);
        currentX += columns[1].width;
        
        doc.text((student.row + 1).toString(), currentX + 2, currentY);
        currentX += columns[2].width;
        
        doc.text((student.column + 1).toString(), currentX + 2, currentY);
        currentX += columns[3].width;
        
        doc.text(student.set ? student.set.toString() : '-', currentX + 2, currentY);
        
        currentY += 7;
    });
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Format time for display
function formatTime(timeString) {
    // Parse time string (HH:MM:SS)
    const [hours, minutes] = timeString.split(':');
    
    // Create date object and set hours and minutes
    const date = new Date();
    date.setHours(parseInt(hours, 10));
    date.setMinutes(parseInt(minutes, 10));
    
    // Format time
    return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

// Format arrangement type for display
function formatArrangementType(type) {
    switch (type) {
        case 'random':
            return 'Random';
        case 'zigzag':
            return 'Zig-zag';
        case 'setwise':
            return 'Set-wise';
        case 'front_to_back':
            return 'Front to Back';
        case 'back_to_front':
            return 'Back to Front';
        default:
            return type;
    }
}

// Export functions
window.PDFExport = {
    generateSeatingPDF
};
