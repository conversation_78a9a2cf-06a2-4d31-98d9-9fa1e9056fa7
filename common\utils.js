/**
 * Utilities Module
 * Common utility functions used across the application
 */

// Show tab content
function showTab(tabId) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // Deactivate all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab content and activate button
    document.getElementById(tabId).classList.add('active');
    document.querySelector(`button[onclick="showTab('${tabId}')"]`).classList.add('active');
}

// Show section in dashboard
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.dashboard-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Deactivate all sidebar links
    document.querySelectorAll('.sidebar a').forEach(link => {
        link.classList.remove('active');
    });
    
    // Show selected section and activate sidebar link
    document.getElementById(sectionId).classList.add('active');
    document.querySelector(`.sidebar a[onclick="showSection('${sectionId}')"]`).classList.add('active');
}

// Close modal
function closeModal() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.remove();
    });
}

// Generate a random ID
function generateId(prefix = '') {
    return prefix + Math.random().toString(36).substr(2, 9);
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString();
}

// Format time
function formatTime(timeString) {
    return timeString;
}

// Export functions
window.Utils = {
    showTab,
    showSection,
    closeModal,
    generateId,
    formatDate,
    formatTime
};
