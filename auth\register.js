/**
 * Registration Module
 * Handles user registration functionality
 */

// Initialize registration functionality
function initRegistration() {
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        // Show/hide student fields based on role selection
        const roleSelect = document.getElementById('registerRole');
        const studentFields = document.querySelector('.student-fields');

        if (roleSelect && studentFields) {
            roleSelect.addEventListener('change', function() {
                if (this.value === 'student') {
                    studentFields.style.display = 'block';
                } else {
                    studentFields.style.display = 'none';
                }
            });
        }

        registerForm.addEventListener('submit', handleRegistration);
    }
}

// Handle registration
async function handleRegistration(e) {
    e.preventDefault();

    const name = document.getElementById('registerName').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('registerConfirmPassword').value;
    const role = document.getElementById('registerRole').value;
    const studentId = document.getElementById('registerStudentId').value;

    // Validate passwords match
    if (password !== confirmPassword) {
        alert('Passwords do not match!');
        return;
    }

    // Validate student ID if role is student
    if (role === 'student' && !studentId) {
        alert('Student ID is required for student registration.');
        return;
    }

    try {
        // Show loading indicator
        const submitButton = e.target.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        submitButton.textContent = 'Registering...';
        submitButton.disabled = true;

        // Check if email is authorized
        const isAuthorized = await window.Auth.isAuthorizedEmail(email, role);

        if (!isAuthorized) {
            alert('This email is not authorized to register. Please contact the administrator.');
            submitButton.textContent = originalText;
            submitButton.disabled = false;
            return;
        }

        // Register user
        const userData = {
            name,
            email,
            password,
            role,
            student_id: role === 'student' ? studentId : null
        };

        await window.Auth.register(userData);

        alert('Registration successful! You can now log in.');

        // Reset form and switch to login tab
        e.target.reset();
        window.Utils.showTab('login');
    } catch (error) {
        alert('Registration failed: ' + (error.message || 'Please try again.'));

        // Reset button
        const submitButton = e.target.querySelector('button[type="submit"]');
        submitButton.textContent = 'Register';
        submitButton.disabled = false;
    }
}

// Export functions
window.Register = {
    initRegistration,
    handleRegistration
};
