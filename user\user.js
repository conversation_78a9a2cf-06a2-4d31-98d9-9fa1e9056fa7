// Sample data for demonstration
let currentStudent = {
    id: 'S001',
    name: '<PERSON>',
    email: '<EMAIL>'
};

// Sample exams data
let exams = [
    { id: 1, name: 'Mathematics Final', date: '2023-06-15', time: '09:00', duration: 180 },
    { id: 2, name: 'Physics Midterm', date: '2023-06-18', time: '14:00', duration: 120 }
];

// Sample rooms data
let rooms = [
    { id: 1, name: 'Room 101', rows: 5, columns: 6, capacity: 30, teacher: 'Dr. <PERSON>' },
    { id: 2, name: 'Hall A', rows: 8, columns: 10, capacity: 80, teacher: 'Prof<PERSON>' }
];

// Sample seating arrangements
let seatingArrangements = {
    'exam1_room1': {
        examId: 1,
        roomId: 1,
        seats: [
            { row: 0, col: 0, studentId: 'S001' },
            { row: 0, col: 2, studentId: 'S002' },
            { row: 1, col: 1, studentId: 'S003' }
        ],
        strategy: 'random'
    },
    'exam2_room2': {
        examId: 2,
        roomId: 2,
        seats: [
            { row: 2, col: 3, studentId: 'S001' },
            { row: 1, col: 5, studentId: 'S002' },
            { row: 3, col: 7, studentId: 'S003' }
        ],
        strategy: 'zigzag'
    }
};

// Load data when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Set student info
    document.getElementById('studentName').textContent = currentStudent.name;
    document.getElementById('studentId').textContent = currentStudent.id;
    
    // Load student's seating data
    loadStudentSeating();
    
    // Load exams for visualization dropdown
    loadExamsDropdown();
});

// Logout function
function logout() {
    window.location.href = '../index.html';
}

// Load student's seating data
function loadStudentSeating() {
    const tableBody = document.getElementById('seatingTable').querySelector('tbody');
    tableBody.innerHTML = '';
    
    // Find all seating arrangements for the current student
    for (const key in seatingArrangements) {
        const arrangement = seatingArrangements[key];
        const seat = arrangement.seats.find(s => s.studentId === currentStudent.id);
        
        if (seat) {
            const exam = exams.find(e => e.id === arrangement.examId);
            const room = rooms.find(r => r.id === arrangement.roomId);
            
            if (exam && room) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${exam.name}</td>
                    <td>${exam.date}</td>
                    <td>${exam.time}</td>
                    <td>${room.name}</td>
                    <td>Row ${seat.row + 1}, Col ${seat.col + 1}</td>
                    <td><button onclick="viewSeat(${exam.id}, ${room.id})" class="btn-view">View</button></td>
                `;
                tableBody.appendChild(row);
            }
        }
    }
    
    // If no seating arrangements found
    if (tableBody.children.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="6">No seating arrangements found.</td>';
        tableBody.appendChild(row);
    }
}

// Load exams dropdown
function loadExamsDropdown() {
    const select = document.getElementById('visualExam');
    select.innerHTML = '<option value="">-- Select Exam --</option>';
    
    // Find exams where the student has a seat
    const studentExams = [];
    
    for (const key in seatingArrangements) {
        const arrangement = seatingArrangements[key];
        const seat = arrangement.seats.find(s => s.studentId === currentStudent.id);
        
        if (seat) {
            const exam = exams.find(e => e.id === arrangement.examId);
            if (exam && !studentExams.some(e => e.id === exam.id)) {
                studentExams.push(exam);
            }
        }
    }
    
    // Add exams to dropdown
    studentExams.forEach(exam => {
        const option = document.createElement('option');
        option.value = exam.id;
        option.textContent = exam.name;
        select.appendChild(option);
    });
}

// View seat when button is clicked
function viewSeat(examId, roomId) {
    document.getElementById('visualExam').value = examId;
    loadStudentVisualization();
}

// Load visualization of student's seat
function loadStudentVisualization() {
    const examId = parseInt(document.getElementById('visualExam').value);
    
    if (isNaN(examId)) {
        document.getElementById('roomVisualization').innerHTML = '<p>Select an exam to view your seat.</p>';
        return;
    }
    
    // Find the seating arrangement for this exam where the student has a seat
    let seatingData = null;
    let roomId = null;
    
    for (const key in seatingArrangements) {
        const arrangement = seatingArrangements[key];
        if (arrangement.examId === examId) {
            const seat = arrangement.seats.find(s => s.studentId === currentStudent.id);
            if (seat) {
                seatingData = arrangement;
                roomId = arrangement.roomId;
                break;
            }
        }
    }
    
    if (!seatingData || !roomId) {
        document.getElementById('roomVisualization').innerHTML = '<p>No seating arrangement found for this exam.</p>';
        return;
    }
    
    const selectedRoom = rooms.find(r => r.id === roomId);
    if (!selectedRoom) {
        return;
    }
    
    // Create visualization
    const visualContainer = document.getElementById('roomVisualization');
    visualContainer.innerHTML = '';
    
    // Add room info
    const roomInfo = document.createElement('div');
    roomInfo.className = 'room-info';
    roomInfo.innerHTML = `
        <h3>Room: ${selectedRoom.name}</h3>
        <p>Teacher: ${selectedRoom.teacher || 'Not assigned'}</p>
        <p>Arrangement Strategy: ${seatingData.strategy || 'Random'}</p>
    `;
    visualContainer.appendChild(roomInfo);
    
    // Create seating grid
    const grid = document.createElement('div');
    grid.className = 'seating-grid';
    grid.style.gridTemplateColumns = `repeat(${selectedRoom.columns}, 1fr)`;
    
    // Create a 2D array to represent the room
    const roomGrid = Array(selectedRoom.rows).fill().map(() => Array(selectedRoom.columns).fill(null));
    
    // Fill in the seats with student data
    seatingData.seats.forEach(seat => {
        if (seat.row < selectedRoom.rows && seat.col < selectedRoom.columns) {
            roomGrid[seat.row][seat.col] = seat;
        }
    });
    
    // Create seat elements
    for (let row = 0; row < selectedRoom.rows; row++) {
        for (let col = 0; col < selectedRoom.columns; col++) {
            const seat = document.createElement('div');
            seat.className = 'seat';
            
            const seatData = roomGrid[row][col];
            if (seatData) {
                seat.classList.add('occupied');
                
                // Highlight current student's seat
                if (seatData.studentId === currentStudent.id) {
                    seat.classList.add('current-student');
                }
                
                seat.innerHTML = `
                    <div class="seat-number">R${row+1}C${col+1}</div>
                    <div class="student-id">${seatData.studentId}</div>
                `;
            } else {
                seat.classList.add('empty');
                seat.innerHTML = `<div class="seat-number">R${row+1}C${col+1}</div>`;
            }
            
            grid.appendChild(seat);
        }
    }
    
    visualContainer.appendChild(grid);
    
    // Add teacher's desk
    const teacherDesk = document.createElement('div');
    teacherDesk.className = 'teacher-desk';
    teacherDesk.textContent = 'Teacher: ' + (selectedRoom.teacher || 'Not assigned');
    visualContainer.appendChild(teacherDesk);
}
