* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Auth styles */
.auth-container {
    max-width: 500px;
    margin: 30px auto;
    background: white;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    padding: 20px;
}

/* Form styles */
.form-group {
    margin-bottom: 15px;
}

/* Tab styles */
.tabs {
    display: flex;
    margin-bottom: 20px;
}

.tab-btn {
    flex: 1;
    padding: 10px;
    background: #f4f4f4;
    border: none;
    cursor: pointer;
}

.tab-btn.active {
    background: #007bff;
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.direct-links {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
    text-align: center;
}

.direct-links a {
    color: #007bff;
    text-decoration: none;
}

.direct-links a:hover {
    text-decoration: underline;
}

/* Registration form styles */
.student-fields {
    display: block;
    transition: all 0.3s ease;
}

