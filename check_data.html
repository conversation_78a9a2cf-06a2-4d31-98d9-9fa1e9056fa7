<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Demo Data - Exam Seating</title>
    <link rel="stylesheet" href="css/main.css">
    <style>
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .data-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .data-section h2 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .btn {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            margin-top: 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Check Demo Data</h1>
        <p>This page displays all the demo data currently stored in localStorage.</p>
        
        <div class="data-section">
            <h2>Courses</h2>
            <table id="coursesTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        
        <div class="data-section">
            <h2>Rooms</h2>
            <table id="roomsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Rows</th>
                        <th>Columns</th>
                        <th>Students/Bench</th>
                        <th>Capacity</th>
                        <th>Teacher</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        
        <div class="data-section">
            <h2>Exams</h2>
            <table id="examsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Date</th>
                        <th>Time</th>
                        <th>Duration (min)</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        
        <div class="data-section">
            <h2>Students</h2>
            <table id="studentsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Course ID</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        
        <div class="data-section">
            <h2>Users</h2>
            <table id="usersTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Role</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        
        <button id="addDataButton" class="btn">Add Demo Data Again</button>
        <a href="index.html" class="btn">Go to Login Page</a>
    </div>
    
    <script>
        // Function to load data from localStorage
        function loadData() {
            // Load courses
            const courses = JSON.parse(localStorage.getItem('courses') || '[]');
            const coursesTable = document.querySelector('#coursesTable tbody');
            coursesTable.innerHTML = '';
            
            courses.forEach(course => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${course.id}</td>
                    <td>${course.name}</td>
                `;
                coursesTable.appendChild(row);
            });
            
            // Load rooms
            const rooms = JSON.parse(localStorage.getItem('rooms') || '[]');
            const roomsTable = document.querySelector('#roomsTable tbody');
            roomsTable.innerHTML = '';
            
            rooms.forEach(room => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${room.id}</td>
                    <td>${room.name}</td>
                    <td>${room.rows}</td>
                    <td>${room.columns}</td>
                    <td>${room.students_per_bench}</td>
                    <td>${room.capacity}</td>
                    <td>${room.teacher}</td>
                `;
                roomsTable.appendChild(row);
            });
            
            // Load exams
            const exams = JSON.parse(localStorage.getItem('exams') || '[]');
            const examsTable = document.querySelector('#examsTable tbody');
            examsTable.innerHTML = '';
            
            exams.forEach(exam => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${exam.id}</td>
                    <td>${exam.name}</td>
                    <td>${exam.date}</td>
                    <td>${exam.time}</td>
                    <td>${exam.duration}</td>
                `;
                examsTable.appendChild(row);
            });
            
            // Load students
            const students = JSON.parse(localStorage.getItem('students') || '[]');
            const studentsTable = document.querySelector('#studentsTable tbody');
            studentsTable.innerHTML = '';
            
            students.forEach(student => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${student.id}</td>
                    <td>${student.name}</td>
                    <td>${student.email}</td>
                    <td>${student.course_id}</td>
                `;
                studentsTable.appendChild(row);
            });
            
            // Load users
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            const usersTable = document.querySelector('#usersTable tbody');
            usersTable.innerHTML = '';
            
            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.id}</td>
                    <td>${user.name}</td>
                    <td>${user.email}</td>
                    <td>${user.role}</td>
                `;
                usersTable.appendChild(row);
            });
        }
        
        // Initialize when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Load data
            loadData();
            
            // Add event listener to the add data button
            const addDataButton = document.getElementById('addDataButton');
            if (addDataButton) {
                addDataButton.addEventListener('click', function() {
                    window.location.href = 'add_demo_data.html';
                });
            }
        });
    </script>
</body>
</html>
