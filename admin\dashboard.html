<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Exam Seating</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/auth.css">
    <link rel="stylesheet" href="admin.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Admin Dashboard</h1>
            <div class="user-info">
                <span>Welcome, <span id="adminName">Administrator</span></span>
                <button onclick="logout()" class="btn-logout">Logout</button>
            </div>
        </header>

        <div class="dashboard">
            <nav class="sidebar">
                <ul>
                    <li><a href="#" class="active" onclick="showSection('exams')">Manage Exams</a></li>
                    <li><a href="#" onclick="showSection('rooms')">Manage Rooms</a></li>
                    <li><a href="#" onclick="showSection('students')">Manage Students</a></li>
                    <li><a href="#" onclick="showSection('authorized')">Authorized Users</a></li>
                    <li><a href="#" onclick="showSection('seating')">Generate Seating</a></li>
                    <li><a href="#" onclick="showSection('visualization')">Visualization</a></li>
                </ul>
            </nav>

            <main class="content">
                <section id="exams" class="dashboard-section active">
                    <h2>Manage Exams</h2>
                    <form id="examForm">
                        <div class="form-group">
                            <label for="examName">Exam Name</label>
                            <input type="text" id="examName" required>
                        </div>
                        <div class="form-group">
                            <label for="examDate">Date</label>
                            <input type="date" id="examDate" required>
                        </div>
                        <div class="form-group">
                            <label for="examTime">Time</label>
                            <input type="time" id="examTime" required>
                        </div>
                        <div class="form-group">
                            <label for="examDuration">Duration (minutes)</label>
                            <input type="number" id="examDuration" required>
                        </div>
                        <button type="submit" class="btn">Add Exam</button>
                    </form>
                    <div class="data-table">
                        <h3>Existing Exams</h3>
                        <table id="examsTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Duration</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Exam data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <section id="rooms" class="dashboard-section">
                    <h2>Manage Rooms</h2>
                    <form id="roomForm">
                        <div class="form-group">
                            <label for="roomName">Room Name/Number</label>
                            <input type="text" id="roomName" required>
                        </div>
                        <div class="form-group">
                            <label for="roomRows">Number of Rows</label>
                            <input type="number" id="roomRows" required min="1">
                        </div>
                        <div class="form-group">
                            <label for="roomColumns">Number of Columns</label>
                            <input type="number" id="roomColumns" required min="1">
                        </div>
                        <div class="form-group">
                            <label for="studentsPerBench">Students Per Bench (max 4)</label>
                            <input type="number" id="studentsPerBench" required min="1" max="4" value="1">
                        </div>
                        <div class="form-group">
                            <label for="teacherName">Teacher In-charge</label>
                            <input type="text" id="teacherName">
                        </div>
                        <button type="submit" class="btn">Add Room</button>
                    </form>
                    <div class="data-table">
                        <h3>Existing Rooms</h3>
                        <table id="roomsTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Rows</th>
                                    <th>Columns</th>
                                    <th>Students/Bench</th>
                                    <th>Capacity</th>
                                    <th>Teacher</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Room data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <section id="students" class="dashboard-section">
                    <h2>Manage Students</h2>
                    <div class="upload-section">
                        <h3>Upload Student Data</h3>
                        <form id="uploadForm">
                            <div class="form-group">
                                <label for="studentFile">Upload CSV/Excel File</label>
                                <input type="file" id="studentFile" accept=".csv,.xlsx,.xls">
                            </div>
                            <button type="submit" class="btn">Upload</button>
                        </form>
                        <div class="template-download">
                            <a href="../templates/student_template.csv" download>Download Template</a>
                        </div>
                    </div>
                    <div class="manual-entry">
                        <h3>Add Student Manually</h3>
                        <form id="studentForm">
                            <div class="form-group">
                                <label for="studentName">Full Name</label>
                                <input type="text" id="studentName" required>
                            </div>
                            <div class="form-group">
                                <label for="studentId">Roll Number/ID</label>
                                <input type="text" id="studentId" required>
                            </div>
                            <div class="form-group">
                                <label for="studentEmail">Email</label>
                                <input type="email" id="studentEmail" required>
                            </div>
                            <div class="form-group">
                                <label for="studentCourse">Course</label>
                                <select id="studentCourse" required>
                                    <option value="">-- Select Course --</option>
                                    <!-- Courses will be loaded here -->
                                </select>
                            </div>
                            <button type="submit" class="btn">Add Student</button>
                        </form>
                    </div>
                    <div class="data-table">
                        <h3>Student List</h3>
                        <table id="studentsTable">
                            <thead>
                                <tr>
                                    <th width="40px"></th>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Course</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Student data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <section id="seating" class="dashboard-section">
                    <h2>Generate Seating Arrangement</h2>
                    <form id="seatingForm">
                        <div class="form-group">
                            <label for="seatingExam">Select Exam</label>
                            <select id="seatingExam" required onchange="loadRoomsDropdown('seatingRoom')">
                                <option value="">-- Select Exam --</option>
                                <!-- Exams will be loaded here -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="seatingRooms">Select Rooms</label>
                            <div id="roomsCheckboxes" class="checkbox-group">
                                <!-- Room checkboxes will be loaded here -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="courseSelection">Course Selection</label>
                            <select id="courseSelection">
                                <option value="all">All Students</option>
                                <option value="specific">Specific Courses</option>
                            </select>
                        </div>

                        <div class="form-group" id="numStudentsGroup">
                            <label for="numStudents">Number of Students</label>
                            <input type="number" id="numStudents" required min="1">
                        </div>

                        <div class="form-group" id="courseListGroup" style="display: none;">
                            <label>Select Courses</label>
                            <div id="courseCheckboxes" class="checkbox-group">
                                <!-- Course checkboxes will be loaded here -->
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="autoAssignRooms">Auto-assign Additional Rooms</label>
                            <div class="checkbox-inline">
                                <input type="checkbox" id="autoAssignRooms" checked>
                                <label for="autoAssignRooms">Automatically assign additional rooms if needed</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Arrangement Strategy</label>
                            <div class="radio-group">
                                <input type="radio" id="randomStrategy" name="strategy" value="random" checked>
                                <label for="randomStrategy">Random</label>

                                <input type="radio" id="rollStrategy" name="strategy" value="roll">
                                <label for="rollStrategy">By Roll Number</label>

                                <input type="radio" id="courseStrategy" name="strategy" value="course">
                                <label for="courseStrategy">By Course</label>

                                <input type="radio" id="zigzagStrategy" name="strategy" value="zigzag">
                                <label for="zigzagStrategy">Zig-Zag Pattern</label>

                                <input type="radio" id="setWiseStrategy" name="strategy" value="setwise">
                                <label for="setWiseStrategy">Set-wise (A,B,C,D)</label>

                                <input type="radio" id="frontToBackStrategy" name="strategy" value="fronttoback">
                                <label for="frontToBackStrategy">Front to Back</label>

                                <input type="radio" id="backToFrontStrategy" name="strategy" value="backtofront">
                                <label for="backToFrontStrategy">Back to Front</label>
                            </div>
                        </div>
                        <button type="submit" class="btn">Generate Seating</button>
                    </form>
                    <div class="actions">
                        <button onclick="exportSeating('pdf')" class="btn">Export to PDF</button>
                        <button onclick="exportSeating('excel')" class="btn">Export to Excel</button>
                    </div>
                </section>

                <section id="authorized" class="dashboard-section">
                    <h2>Manage Authorized Users</h2>
                    <div class="upload-section">
                        <h3>Upload Authorized Users</h3>
                        <form id="authorizedUsersForm">
                            <div class="form-group">
                                <label for="userType">User Type</label>
                                <select id="userType" required onchange="updateTemplateInfo()">
                                    <option value="student">Students</option>
                                    <option value="teacher">Teachers</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="authorizedFile">Upload CSV/Excel File</label>
                                <input type="file" id="authorizedFile" accept=".csv,.xlsx,.xls" required>
                            </div>
                            <div class="template-info" id="templateInfo">
                                <p><strong>Student Template Format:</strong> Serial No, Name, Email, USN/Roll Number, Course</p>
                            </div>
                            <button type="submit" class="btn">Upload</button>
                        </form>
                        <div class="template-download">
                            <a href="../templates/student_authorized_template.csv" download>Download Student Template</a> |
                            <a href="../templates/teacher_authorized_template.csv" download>Download Teacher Template</a>
                        </div>
                    </div>

                    <div class="data-table">
                        <h3>Authorized Students</h3>
                        <table id="authorizedStudentsTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Roll Number/ID</th>
                                    <th>Course</th>
                                    <th>Registered</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Authorized students will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <div class="data-table">
                        <h3>Authorized Teachers</h3>
                        <table id="authorizedTeachersTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Department</th>
                                    <th>Registered</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Authorized teachers will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <section id="visualization" class="dashboard-section">
                    <h2>Seating Visualization</h2>
                    <div class="form-group">
                        <label for="visualExam">Select Exam</label>
                        <select id="visualExam" onchange="loadVisualization()">
                            <option value="">-- Select Exam --</option>
                            <!-- Exams will be loaded here -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="visualRoom">Select Room</label>
                        <select id="visualRoom" onchange="loadVisualization()">
                            <option value="">-- Select Room --</option>
                            <!-- Rooms will be loaded here -->
                        </select>
                    </div>
                    <div class="visualization-container">
                        <div id="roomVisualization" class="room-visual">
                            <!-- Room visualization will be rendered here -->
                        </div>
                    </div>
                    <div class="actions">
                        <button onclick="printVisualization()" class="btn">Print</button>
                        <button onclick="exportVisualization('pdf')" class="btn">Export to PDF</button>
                    </div>
                </section>
            </main>
        </div>
    </div>
    <!-- External libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- Common modules -->
    <script src="../common/utils.js"></script>
    <script src="../common/server.js"></script>
    <script src="../common/data.js"></script>
    <script src="../common/pdf-export.js"></script>
    <script src="../common/excel-export.js"></script>
    <script src="../common/visualization.js"></script>

    <!-- Authentication module -->
    <script src="../auth/auth.js"></script>

    <!-- Admin modules -->
    <script src="dashboard.js"></script>
    <script src="exams.js"></script>
    <script src="rooms.js"></script>
    <script src="students.js"></script>
    <script src="authorized-users.js"></script>
    <script src="seating.js"></script>
    <script src="visualization.js"></script>

    <!-- Initialize dashboard -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize dashboard
            window.Dashboard.initDashboard();

            // Initialize modules
            window.Exams.initExams();
            window.Rooms.initRooms();
            window.Students.initStudents();
            window.AuthorizedUsers.initAuthorizedUsers();
            window.Seating.initSeating();
            window.Visualization.initVisualization();
        });
    </script>
</body>
</html>

