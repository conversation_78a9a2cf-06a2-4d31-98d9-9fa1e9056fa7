/**
 * Visualization Module
 * Handles visualization of seating arrangements
 */

// Set colors for different sets
const SET_COLORS = {
    'A': '#ffcccc', // Light red
    'B': '#ccffcc', // Light green
    'C': '#ccccff', // Light blue
    'D': '#ffffcc', // Light yellow
    1: '#ffcccc',   // Light red
    2: '#ccffcc',   // Light green
    3: '#ccccff',   // Light blue
    4: '#ffffcc'    // Light yellow
};

// Render seating arrangement visualization
function renderSeatingVisualization(containerId, arrangement, room) {
    const container = document.getElementById(containerId);
    if (!container) {
        console.error(`Container with ID '${containerId}' not found`);
        return;
    }
    
    // Clear container
    container.innerHTML = '';
    
    // Create room container
    const roomContainer = document.createElement('div');
    roomContainer.className = 'room-container';
    roomContainer.style.display = 'grid';
    roomContainer.style.gridTemplateColumns = `repeat(${room.columns}, 1fr)`;
    roomContainer.style.gridGap = '5px';
    roomContainer.style.padding = '20px';
    roomContainer.style.backgroundColor = '#f5f5f5';
    roomContainer.style.borderRadius = '5px';
    roomContainer.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.1)';
    
    // Add room name
    const roomName = document.createElement('div');
    roomName.className = 'room-name';
    roomName.textContent = `Room: ${room.name}`;
    roomName.style.gridColumn = `1 / span ${room.columns}`;
    roomName.style.textAlign = 'center';
    roomName.style.fontWeight = 'bold';
    roomName.style.marginBottom = '10px';
    roomName.style.fontSize = '1.2em';
    roomContainer.appendChild(roomName);
    
    // Add teacher's desk
    const teacherDesk = document.createElement('div');
    teacherDesk.className = 'teacher-desk';
    teacherDesk.textContent = `Teacher: ${room.teacher}`;
    teacherDesk.style.gridColumn = `1 / span ${room.columns}`;
    teacherDesk.style.backgroundColor = '#d1e7dd';
    teacherDesk.style.padding = '10px';
    teacherDesk.style.textAlign = 'center';
    teacherDesk.style.borderRadius = '5px';
    teacherDesk.style.marginBottom = '20px';
    roomContainer.appendChild(teacherDesk);
    
    // Create benches
    for (let row = 0; row < room.rows; row++) {
        for (let col = 0; col < room.columns; col++) {
            // Create bench
            const bench = document.createElement('div');
            bench.className = 'bench';
            bench.style.backgroundColor = '#ffffff';
            bench.style.border = '1px solid #ddd';
            bench.style.borderRadius = '5px';
            bench.style.padding = '5px';
            bench.style.minHeight = '80px';
            bench.style.position = 'relative';
            
            // Find students assigned to this bench
            const benchStudents = arrangement.seats.filter(seat => 
                seat.row === row && seat.column === col
            );
            
            // Add bench label
            const benchLabel = document.createElement('div');
            benchLabel.className = 'bench-label';
            benchLabel.textContent = `R${row + 1}C${col + 1}`;
            benchLabel.style.position = 'absolute';
            benchLabel.style.top = '2px';
            benchLabel.style.right = '2px';
            benchLabel.style.fontSize = '0.7em';
            benchLabel.style.color = '#999';
            bench.appendChild(benchLabel);
            
            if (benchStudents.length > 0) {
                // Group students by set
                const studentsBySet = {};
                benchStudents.forEach(student => {
                    const set = student.set || 'None';
                    if (!studentsBySet[set]) {
                        studentsBySet[set] = [];
                    }
                    studentsBySet[set].push(student);
                });
                
                // Create student list
                const studentList = document.createElement('div');
                studentList.className = 'student-list';
                studentList.style.display = 'flex';
                studentList.style.flexDirection = 'column';
                studentList.style.gap = '5px';
                
                // Add students grouped by set
                Object.keys(studentsBySet).forEach(set => {
                    const setContainer = document.createElement('div');
                    setContainer.className = 'set-container';
                    setContainer.style.backgroundColor = SET_COLORS[set] || '#f8f9fa';
                    setContainer.style.borderRadius = '3px';
                    setContainer.style.padding = '3px';
                    
                    // Add set label if not 'None'
                    if (set !== 'None') {
                        const setLabel = document.createElement('div');
                        setLabel.className = 'set-label';
                        setLabel.textContent = `Set ${set}`;
                        setLabel.style.fontWeight = 'bold';
                        setLabel.style.fontSize = '0.8em';
                        setLabel.style.marginBottom = '2px';
                        setContainer.appendChild(setLabel);
                    }
                    
                    // Add students in this set
                    studentsBySet[set].forEach(student => {
                        const studentItem = document.createElement('div');
                        studentItem.className = 'student-item';
                        studentItem.textContent = `${student.student_id}: ${student.student_name}`;
                        studentItem.style.fontSize = '0.8em';
                        studentItem.style.whiteSpace = 'nowrap';
                        studentItem.style.overflow = 'hidden';
                        studentItem.style.textOverflow = 'ellipsis';
                        studentItem.title = `${student.student_id}: ${student.student_name}${student.course_name ? ` (${student.course_name})` : ''}`;
                        setContainer.appendChild(studentItem);
                    });
                    
                    studentList.appendChild(setContainer);
                });
                
                bench.appendChild(studentList);
            } else {
                // Empty bench
                bench.style.backgroundColor = '#f8f9fa';
                
                const emptyLabel = document.createElement('div');
                emptyLabel.className = 'empty-label';
                emptyLabel.textContent = 'Empty';
                emptyLabel.style.color = '#aaa';
                emptyLabel.style.textAlign = 'center';
                emptyLabel.style.marginTop = '30px';
                emptyLabel.style.fontSize = '0.9em';
                bench.appendChild(emptyLabel);
            }
            
            roomContainer.appendChild(bench);
        }
    }
    
    // Add legend
    const legend = document.createElement('div');
    legend.className = 'legend';
    legend.style.gridColumn = `1 / span ${room.columns}`;
    legend.style.marginTop = '20px';
    legend.style.display = 'flex';
    legend.style.flexWrap = 'wrap';
    legend.style.gap = '10px';
    legend.style.justifyContent = 'center';
    
    // Add legend title
    const legendTitle = document.createElement('div');
    legendTitle.className = 'legend-title';
    legendTitle.textContent = 'Legend:';
    legendTitle.style.fontWeight = 'bold';
    legendTitle.style.marginRight = '10px';
    legend.appendChild(legendTitle);
    
    // Add set colors to legend
    Object.keys(SET_COLORS).forEach(set => {
        if (set === '1' || set === '2' || set === '3' || set === '4' || 
            set === 'A' || set === 'B' || set === 'C' || set === 'D') {
            const setItem = document.createElement('div');
            setItem.className = 'legend-item';
            setItem.style.display = 'flex';
            setItem.style.alignItems = 'center';
            setItem.style.marginRight = '10px';
            
            const setColor = document.createElement('div');
            setColor.className = 'set-color';
            setColor.style.width = '15px';
            setColor.style.height = '15px';
            setColor.style.backgroundColor = SET_COLORS[set];
            setColor.style.marginRight = '5px';
            setColor.style.borderRadius = '3px';
            setItem.appendChild(setColor);
            
            const setText = document.createElement('div');
            setText.className = 'set-text';
            setText.textContent = `Set ${set}`;
            setText.style.fontSize = '0.8em';
            setItem.appendChild(setText);
            
            legend.appendChild(setItem);
        }
    });
    
    roomContainer.appendChild(legend);
    
    // Add statistics
    const stats = document.createElement('div');
    stats.className = 'statistics';
    stats.style.gridColumn = `1 / span ${room.columns}`;
    stats.style.marginTop = '10px';
    stats.style.padding = '10px';
    stats.style.backgroundColor = '#e9ecef';
    stats.style.borderRadius = '5px';
    stats.style.fontSize = '0.9em';
    
    // Calculate statistics
    const totalSeats = room.rows * room.columns * room.students_per_bench;
    const occupiedSeats = arrangement.seats.length;
    const occupancyRate = Math.round((occupiedSeats / totalSeats) * 100);
    
    stats.innerHTML = `
        <div><strong>Total Capacity:</strong> ${totalSeats} students</div>
        <div><strong>Occupied Seats:</strong> ${occupiedSeats} students</div>
        <div><strong>Occupancy Rate:</strong> ${occupancyRate}%</div>
    `;
    
    roomContainer.appendChild(stats);
    
    // Add container to DOM
    container.appendChild(roomContainer);
}

// Export functions
window.Visualization = {
    renderSeatingVisualization
};
