<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exam Seating Arrangement System</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/auth.css">
</head>
<body>
    <div class="container">
        <h1>Exam Seating Arrangement System</h1>
        <div class="auth-container">
            <div class="tabs">
                <button class="tab-btn active" onclick="showTab('login')">User Login</button>
                <button class="tab-btn" onclick="showTab('adminLogin')">Admin Login</button>
                <button class="tab-btn" onclick="showTab('register')">Register</button>
            </div>
            <!-- User Login Form -->
            <div id="login" class="tab-content active">
                <form id="loginForm">
                    <div class="form-group">
                        <label for="loginEmail">Email</label>
                        <input type="email" id="loginEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="loginPassword">Password</label>
                        <input type="password" id="loginPassword" required>
                    </div>
                    <button type="submit" class="btn">Login</button>
                </form>
                <div class="direct-links">
                    <p>Quick access (for development only):
                        <a href="admin/dashboard.html">Admin Dashboard</a> |
                        <a href="user/dashboard.html">Student Dashboard</a>
                    </p>
                </div>
            </div>

            <!-- Admin Login Form -->
            <div id="adminLogin" class="tab-content">
                <form id="adminLoginForm">
                    <div class="form-group">
                        <label for="adminEmail">Admin Email</label>
                        <input type="email" id="adminEmail" required placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="adminPassword">Password</label>
                        <input type="password" id="adminPassword" required placeholder="admin@1">
                    </div>
                    <button type="submit" class="btn">Login</button>
                </form>
            </div>
            <!-- Register Form -->
            <div id="register" class="tab-content">
                <form id="registerForm">
                    <div class="form-group">
                        <label for="registerName">Full Name</label>
                        <input type="text" id="registerName" required>
                    </div>
                    <div class="form-group">
                        <label for="registerEmail">Email</label>
                        <input type="email" id="registerEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="registerPassword">Password</label>
                        <input type="password" id="registerPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="registerConfirmPassword">Confirm Password</label>
                        <input type="password" id="registerConfirmPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="registerRole">Role</label>
                        <select id="registerRole" required>
                            <option value="student">Student</option>
                            <option value="teacher">Teacher</option>
                        </select>
                    </div>
                    <div class="form-group student-fields">
                        <label for="registerStudentId">Student ID/Roll Number</label>
                        <input type="text" id="registerStudentId">
                    </div>
                    <button type="submit" class="btn">Register</button>
                </form>
            </div>
        </div>
    </div>
    <!-- External libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- Common modules -->
    <script src="common/utils.js"></script>
    <script src="common/server.js"></script>
    <script src="common/data.js"></script>
    <script src="common/pdf-export.js"></script>
    <script src="common/excel-export.js"></script>
    <script src="common/visualization.js"></script>

    <!-- Authentication modules -->
    <script src="auth/auth.js"></script>
    <script src="auth/login.js"></script>
    <script src="auth/register.js"></script>

    <!-- Main script -->
    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            // Load sample data
            window.Data.loadSampleData();

            // Initialize login and registration
            window.Login.initLogin();
            window.Register.initRegistration();
        });
    </script>
</body>
</html>
