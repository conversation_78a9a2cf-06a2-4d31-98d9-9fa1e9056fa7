/* Admin dashboard specific styles */
.dashboard {
    display: flex;
    margin-top: 20px;
    background: white;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.sidebar {
    width: 250px;
    background: #333;
    padding: 20px 0;
    border-radius: 5px 0 0 5px;
}

.sidebar ul {
    list-style: none;
}

.sidebar a {
    display: block;
    color: white;
    padding: 15px 20px;
    text-decoration: none;
    transition: background 0.3s;
}

.sidebar a:hover, .sidebar a.active {
    background: #007bff;
}

.content {
    flex: 1;
    padding: 20px;
}

.dashboard-section {
    display: none;
}

.dashboard-section.active {
    display: block;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info span {
    font-size: 14px;
}

#adminName {
    font-weight: bold;
    color: #007bff;
}

.btn-logout {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 3px;
    cursor: pointer;
}

/* Form styles */
form {
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 5px;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

input, select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 3px;
    cursor: pointer;
}

/* Table styles */
.data-table {
    margin-top: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background: #f4f4f4;
}

/* Visualization styles */
.room-visual {
    margin-top: 20px;
    border: 1px solid #ddd;
    padding: 20px;
    min-height: 400px;
    background: #f9f9f9;
}

.room-info {
    margin-bottom: 20px;
    padding: 15px;
    background: #e9f5ff;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.room-info h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #0056b3;
    border-bottom: 1px solid #b3d7ff;
    padding-bottom: 8px;
}

.room-info p {
    margin: 5px 0;
}

.course-distribution, .set-distribution {
    margin-top: 15px;
    padding: 10px;
    background: #f0f8ff;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}

.course-distribution h4, .set-distribution h4 {
    margin-top: 0;
    margin-bottom: 8px;
    color: #0056b3;
    font-size: 14px;
}

.course-distribution ul, .set-distribution ul {
    margin: 0;
    padding-left: 20px;
}

.course-distribution li, .set-distribution li {
    margin-bottom: 4px;
}

.set-distribution {
    border-left-color: #28a745;
    background: #f0fff4;
}

.seating-grid {
    display: grid;
    gap: 10px;
    margin-top: 20px;
}

.bench {
    background: #f5f5f5;
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.bench-header {
    font-weight: bold;
    padding: 5px;
    background: #e0e0e0;
    border-radius: 3px;
    margin-bottom: 10px;
    text-align: center;
}

.bench-content {
    display: flex;
    flex-direction: row;
    gap: 10px;
    justify-content: space-between;
}

.student-seat {
    background: #e0e0e0;
    border-radius: 5px;
    padding: 10px;
    text-align: center;
    min-height: 80px;
    min-width: 100px;
    flex: 1;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.student-seat:hover {
    transform: scale(1.05);
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.student-seat.occupied {
    background: #b8e0d2;
}

.student-seat.empty {
    background: #f0f0f0;
}

.seat-position {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
    font-weight: bold;
}

.empty-text {
    color: #999;
    font-style: italic;
    margin-top: 10px;
}

.student-id {
    font-weight: bold;
    margin-bottom: 3px;
}

.student-name {
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.student-set {
    font-size: 14px;
    font-weight: bold;
    margin-top: 5px;
    padding: 3px 8px;
    border-radius: 4px;
    display: inline-block;
    color: #000;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

/* Set colors for set-wise arrangement */
.student-seat.set-a, .seat.set-a {
    background: #e57373; /* Stronger red */
    border: 2px solid #c62828;
}

.student-seat.set-b, .seat.set-b {
    background: #81c784; /* Stronger green */
    border: 2px solid #2e7d32;
}

.student-seat.set-c, .seat.set-c {
    background: #64b5f6; /* Stronger blue */
    border: 2px solid #1565c0;
}

.student-seat.set-d, .seat.set-d {
    background: #fff176; /* Stronger yellow */
    border: 2px solid #f9a825;
}

.seating-legend {
    margin-top: 20px;
    padding: 15px;
    background: #f0f0f0;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.seating-legend h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
    text-align: center;
}

.legend-items {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
    margin-bottom: 10px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: bold;
}

.legend-note {
    font-size: 12px;
    font-style: italic;
    text-align: center;
    margin-top: 10px;
    margin-bottom: 0;
}

/* Pattern example in legend */
.bench-pattern {
    margin-top: 15px;
    padding: 10px;
    background: #f8f8f8;
    border-radius: 5px;
    border: 1px dashed #ccc;
}

.bench-pattern h4 {
    margin-top: 0;
    margin-bottom: 10px;
    text-align: center;
    font-size: 14px;
}

.pattern-example {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
}

.pattern-example h5 {
    margin: 0 0 10px 0;
    font-size: 14px;
    text-align: center;
}

.pattern-grid {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 0 auto;
}

.pattern-row {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.pattern-bench {
    display: flex;
    background: #e0e0e0;
    border-radius: 5px;
    padding: 5px;
    gap: 5px;
    width: 30px;
    height: 30px;
    justify-content: center;
    align-items: center;
}

.pattern-seat {
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    font-weight: bold;
    font-size: 12px;
    color: #333;
}

.set-distribution-table {
    margin: 10px 0;
}

.set-distribution-table table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
}

.set-distribution-table th,
.set-distribution-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.set-distribution-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.set-distribution-table td:last-child {
    width: 50px;
    text-align: center;
}

.bench-pattern ul {
    margin-top: 5px;
    padding-left: 20px;
}

.bench-pattern li {
    margin-bottom: 5px;
}

.legend-color {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 3px;
}

.legend-color.set-a {
    background: #e57373;
    border: 2px solid #c62828;
}

.legend-color.set-b {
    background: #81c784;
    border: 2px solid #2e7d32;
}

.legend-color.set-c {
    background: #64b5f6;
    border: 2px solid #1565c0;
}

.legend-color.set-d {
    background: #fff176;
    border: 2px solid #f9a825;
}

.teacher-desk {
    width: 120px;
    height: 60px;
    margin: 20px auto;
    background: #d1c4e9;
    text-align: center;
    line-height: 60px;
    border-radius: 3px;
}

/* Modal styles for seat editing */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 20px;
    border-radius: 5px;
    width: 400px;
    max-width: 90%;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.btn-cancel {
    background: #6c757d;
}

/* Checkbox styles */
.checkbox-group {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 5px;
    background: #f9f9f9;
}

.checkbox-group label {
    display: block;
    margin-bottom: 8px;
    cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 8px;
}

.checkbox-inline {
    display: flex;
    align-items: center;
    gap: 8px;
}

.checkbox-inline input[type="checkbox"] {
    margin: 0;
}

.radio-group {
    display: flex;
    gap: 15px;
}

.radio-group input {
    width: auto;
}

.actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

.upload-section, .manual-entry {
    margin-bottom: 30px;
}

.template-download {
    margin-top: 10px;
}

.template-download a {
    color: #007bff;
    text-decoration: none;
}

/* Action buttons */
.btn-edit, .btn-delete {
    padding: 5px 10px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    margin-right: 5px;
}

.btn-edit {
    background: #28a745;
    color: white;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-view {
    background: #17a2b8;
    color: white;
    padding: 5px 10px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

/* Authorized users styles */
.status-registered {
    color: #28a745;
    font-weight: bold;
}

.status-pending {
    color: #dc3545;
}

.template-info {
    margin: 10px 0;
    padding: 10px;
    background-color: #f8f9fa;
    border-left: 4px solid #17a2b8;
    border-radius: 4px;
}

.template-info p {
    margin: 0;
    font-size: 14px;
}