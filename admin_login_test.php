<?php
// Start session
session_start();

// Include database configuration
require_once 'api/config.php';

// Function to test admin login
function testAdminLogin() {
    // Admin credentials
    $email = '<EMAIL>';
    $password = 'admin@1';
    
    echo "<h2>Testing Admin Login</h2>";
    echo "<p>Using credentials: $email / $password</p>";
    
    try {
        // Connect to database
        $conn = connectDB();
        
        // Check if admin user exists
        $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            echo "<p style='color: red;'>Admin user does not exist in the database.</p>";
            echo "<p>Please initialize the database by visiting <a href='api/init.php'>api/init.php</a></p>";
            return;
        }
        
        echo "<p>Admin user found in database:</p>";
        echo "<pre>";
        print_r($user);
        echo "</pre>";
        
        // Verify password
        $passwordVerified = password_verify($password, $user['password']);
        echo "<p>Password verification: " . ($passwordVerified ? "SUCCESS" : "FAILED") . "</p>";
        
        if ($passwordVerified) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_role'] = $user['role'];
            
            echo "<p style='color: green;'>Admin login successful! Session variables set.</p>";
            echo "<p>You can now access the <a href='admin/dashboard.html'>Admin Dashboard</a></p>";
        } else {
            echo "<p style='color: red;'>Admin login failed: Password verification failed.</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
    }
}

// Test admin login
testAdminLogin();

// Display session information
echo "<h2>Session Information</h2>";
echo "<pre>";
echo "Session ID: " . session_id() . "\n";
echo "Session Status: " . session_status() . "\n";
echo "Session Variables:\n";
print_r($_SESSION);
echo "</pre>";

// Provide direct access link
echo "<p>Direct access link (bypasses authentication): <a href='admin/dashboard.html'>Admin Dashboard</a></p>";
?>
