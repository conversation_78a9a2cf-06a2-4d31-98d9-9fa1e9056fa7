/**
 * Excel Export Module
 * Handles exporting seating arrangements to Excel
 */

// Generate Excel for seating arrangement
async function generateSeatingExcel(arrangementId) {
    try {
        // Get seating arrangement data
        const arrangements = await window.Data.getData('seatingArrangements');
        const arrangement = arrangements.find(a => a.id === arrangementId);
        
        if (!arrangement) {
            throw new Error('Seating arrangement not found');
        }
        
        // Get exam data
        const exams = await window.Data.getData('exams');
        const exam = exams.find(e => e.id === arrangement.exam_id);
        
        if (!exam) {
            throw new Error('Exam not found');
        }
        
        // Get room data
        const rooms = await window.Data.getData('rooms');
        const room = rooms.find(r => r.id === arrangement.room_id);
        
        if (!room) {
            throw new Error('Room not found');
        }
        
        // Get course data
        const courses = await window.Data.getData('courses');
        const course = courses.find(c => c.id === arrangement.course_id);
        
        // Create a new workbook
        const XLSX = window.XLSX;
        const workbook = XLSX.utils.book_new();
        
        // Create summary worksheet
        const summaryData = [
            ['Exam Seating Arrangement'],
            [],
            ['Exam Details'],
            ['Exam Name', exam.name],
            ['Date', formatDate(exam.date)],
            ['Time', formatTime(exam.time)],
            ['Duration', `${exam.duration} minutes`],
            [],
            ['Room Details'],
            ['Room Name', room.name],
            ['Capacity', room.capacity],
            ['Teacher', room.teacher],
            ['Rows', room.rows],
            ['Columns', room.columns],
            ['Students per Bench', room.students_per_bench],
            [],
            ['Course Details'],
            ['Course Name', course ? course.name : 'All Courses'],
            [],
            ['Arrangement Details'],
            ['Arrangement Type', formatArrangementType(arrangement.type)],
            ['Total Students', arrangement.seats.length]
        ];
        
        const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
        XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
        
        // Create seating chart worksheet
        const chartData = createSeatingChartData(room, arrangement);
        const chartSheet = XLSX.utils.aoa_to_sheet(chartData);
        XLSX.utils.book_append_sheet(workbook, chartSheet, 'Seating Chart');
        
        // Create student list worksheet
        const studentData = createStudentListData(arrangement);
        const studentSheet = XLSX.utils.aoa_to_sheet(studentData);
        XLSX.utils.book_append_sheet(workbook, studentSheet, 'Student List');
        
        // Save the Excel file
        XLSX.writeFile(workbook, `seating_arrangement_${arrangementId}.xlsx`);
        
        return true;
    } catch (error) {
        console.error('Error generating Excel:', error);
        alert('Failed to generate Excel: ' + error.message);
        return false;
    }
}

// Create seating chart data for Excel
function createSeatingChartData(room, arrangement) {
    // Create header row with column numbers
    const chartData = [
        ['Seating Chart', ...Array.from({ length: room.columns }, (_, i) => `Column ${i + 1}`)]
    ];
    
    // Create rows with student data
    for (let row = 0; row < room.rows; row++) {
        const rowData = [`Row ${row + 1}`];
        
        for (let col = 0; col < room.columns; col++) {
            // Find students assigned to this bench
            const benchStudents = arrangement.seats.filter(seat => 
                seat.row === row && seat.column === col
            );
            
            if (benchStudents.length > 0) {
                // Format student info
                const studentInfo = benchStudents.map(student => 
                    `${student.student_id}: ${student.student_name}${student.set ? ` (Set ${student.set})` : ''}`
                ).join('\n');
                
                rowData.push(studentInfo);
            } else {
                rowData.push('');
            }
        }
        
        chartData.push(rowData);
    }
    
    return chartData;
}

// Create student list data for Excel
function createStudentListData(arrangement) {
    // Create header row
    const studentData = [
        ['Student ID', 'Name', 'Row', 'Column', 'Set', 'Course']
    ];
    
    // Sort students by ID
    const sortedStudents = [...arrangement.seats].sort((a, b) => 
        a.student_id.localeCompare(b.student_id)
    );
    
    // Add student rows
    sortedStudents.forEach(student => {
        studentData.push([
            student.student_id,
            student.student_name,
            student.row + 1, // Convert to 1-based indexing
            student.column + 1, // Convert to 1-based indexing
            student.set || '-',
            student.course_name || '-'
        ]);
    });
    
    return studentData;
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Format time for display
function formatTime(timeString) {
    // Parse time string (HH:MM:SS)
    const [hours, minutes] = timeString.split(':');
    
    // Create date object and set hours and minutes
    const date = new Date();
    date.setHours(parseInt(hours, 10));
    date.setMinutes(parseInt(minutes, 10));
    
    // Format time
    return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

// Format arrangement type for display
function formatArrangementType(type) {
    switch (type) {
        case 'random':
            return 'Random';
        case 'zigzag':
            return 'Zig-zag';
        case 'setwise':
            return 'Set-wise';
        case 'front_to_back':
            return 'Front to Back';
        case 'back_to_front':
            return 'Back to Front';
        default:
            return type;
    }
}

// Export functions
window.ExcelExport = {
    generateSeatingExcel
};
