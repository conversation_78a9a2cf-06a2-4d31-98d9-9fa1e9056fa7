// Sample data for demonstration
let exams = [];
let rooms = [];
let students = [];
let courses = [];
let seatingArrangements = {};
let authorizedStudents = [];
let authorizedTeachers = [];

// Show different sections in admin dashboard
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.dashboard-section').forEach(section => {
        section.classList.remove('active');
    });

    // Show selected section
    document.getElementById(sectionId).classList.add('active');

    // Update active nav link
    document.querySelectorAll('.sidebar a').forEach(link => {
        link.classList.remove('active');
    });
    document.querySelector(`.sidebar a[onclick="showSection('${sectionId}')"]`).classList.add('active');

    // Load data for specific sections
    if (sectionId === 'seating') {
        loadExamsDropdown('seatingExam');
    } else if (sectionId === 'visualization') {
        loadExamsDropdown('visualExam');
        loadRoomsDropdown('visualRoom');
    }
}

// Logout function
function logout() {
    // Clear user data from localStorage
    localStorage.removeItem('currentUser');

    // Redirect to login page
    window.location.href = '../index.html';
}

// Load data when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in and is an admin
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');

    if (!currentUser || !currentUser.email || (currentUser.role !== 'admin' && currentUser.role !== 'teacher')) {
        alert('You must be logged in as an admin to access this page.');
        window.location.href = '../index.html';
        return;
    }

    // Display admin name
    const adminNameElement = document.getElementById('adminName');
    if (adminNameElement && currentUser.name) {
        adminNameElement.textContent = currentUser.name;
    }

    // Add event listeners for forms
    document.getElementById('examForm').addEventListener('submit', addExam);
    document.getElementById('roomForm').addEventListener('submit', addRoom);
    document.getElementById('studentForm').addEventListener('submit', addStudent);
    document.getElementById('uploadForm').addEventListener('submit', uploadStudents);
    document.getElementById('seatingForm').addEventListener('submit', generateSeatingArrangement);
    document.getElementById('authorizedUsersForm').addEventListener('submit', uploadAuthorizedUsers);

    // Load sample data
    loadSampleData();

    // Load authorized users from localStorage
    loadAuthorizedUsers();

    // Render tables
    renderExamsTable();
    renderRoomsTable();
    renderStudentsTable();
    renderAuthorizedUsersTable();

    // Load dropdowns
    loadExamsDropdown('seatingExam');
    loadExamsDropdown('visualExam');
    loadRoomsDropdown('visualRoom');
    loadCoursesDropdown('studentCourse');
    loadCourseCheckboxes();

    // Handle course selection toggle
    const courseSelection = document.getElementById('courseSelection');
    if (courseSelection) {
        courseSelection.addEventListener('change', function() {
            const numStudentsGroup = document.getElementById('numStudentsGroup');
            const courseListGroup = document.getElementById('courseListGroup');

            if (this.value === 'all') {
                numStudentsGroup.style.display = 'block';
                courseListGroup.style.display = 'none';
            } else {
                numStudentsGroup.style.display = 'none';
                courseListGroup.style.display = 'block';
            }
        });
    }
});

// Load sample data for demonstration
function loadSampleData() {
    exams = [
        { id: 1, name: 'Mathematics Final', date: '2023-06-15', time: '09:00', duration: 180 },
        { id: 2, name: 'Physics Midterm', date: '2023-06-18', time: '14:00', duration: 120 }
    ];

    rooms = [
        { id: 1, name: 'Room 101', rows: 5, columns: 6, studentsPerBench: 2, capacity: 60, teacher: 'Dr. Smith' },
        { id: 2, name: 'Hall A', rows: 8, columns: 10, studentsPerBench: 4, capacity: 320, teacher: 'Prof. Johnson' }
    ];

    // Sample courses
    courses = [
        { id: 1, name: 'Computer Science' },
        { id: 2, name: 'artificial intelligence' },
        { id: 3, name: 'Physics' },
        { id: 4, name: 'Chemistry' },
        { id: 5, name: 'Biology' }
    ];

    students = [
        { id: 'S001', name: 'John Doe', email: '<EMAIL>', course: 1 },
        { id: 'S002', name: 'Jane Smith', email: '<EMAIL>', course: 1 },
        { id: 'S003', name: 'Bob Johnson', email: '<EMAIL>', course: 2 },
        { id: 'S004', name: 'Alice Brown', email: '<EMAIL>', course: 2 },
        { id: 'S005', name: 'Charlie Davis', email: '<EMAIL>', course: 3 },
        { id: 'S006', name: 'Eva Wilson', email: '<EMAIL>', course: 3 },
        { id: 'S007', name: 'Frank Miller', email: '<EMAIL>', course: 4 },
        { id: 'S008', name: 'Grace Lee', email: '<EMAIL>', course: 4 },
        { id: 'S009', name: 'Henry Taylor', email: '<EMAIL>', course: 5 },
        { id: 'S010', name: 'Ivy Robinson', email: '<EMAIL>', course: 5 },
        { id: 'S011', name: 'Jack White', email: '<EMAIL>', course: 1 },
        { id: 'S012', name: 'Karen Green', email: '<EMAIL>', course: 2 },
        { id: 'S013', name: 'Leo Black', email: '<EMAIL>', course: 3 },
        { id: 'S014', name: 'Mia Gray', email: '<EMAIL>', course: 4 },
        { id: 'S015', name: 'Noah King', email: '<EMAIL>', course: 5 }
    ];

    // Sample authorized users
    authorizedStudents = [
        { id: 'S001', name: 'John Doe', email: '<EMAIL>', course: 1, registered: true },
        { id: 'S002', name: 'Jane Smith', email: '<EMAIL>', course: 1, registered: false },
        { id: 'S003', name: 'Bob Johnson', email: '<EMAIL>', course: 2, registered: false }
    ];

    authorizedTeachers = [
        { id: 'T001', name: 'Dr. Smith', email: '<EMAIL>', department: 'Mathematics', registered: true },
        { id: 'T002', name: 'Prof. Johnson', email: '<EMAIL>', department: 'Physics', registered: false }
    ];

    // Sample seating arrangement
    seatingArrangements = {
        'exam1_room1': {
            examId: 1,
            roomId: 1,
            seats: [
                { row: 0, col: 0, studentId: 'S001' },
                { row: 0, col: 2, studentId: 'S002' },
                { row: 1, col: 1, studentId: 'S003' }
            ]
        }
    };
}

// Add new exam
function addExam(e) {
    e.preventDefault();

    const exam = {
        id: exams.length + 1,
        name: document.getElementById('examName').value,
        date: document.getElementById('examDate').value,
        time: document.getElementById('examTime').value,
        duration: document.getElementById('examDuration').value
    };

    exams.push(exam);
    renderExamsTable();

    // Reset form
    document.getElementById('examForm').reset();
}

// Add new room
function addRoom(e) {
    e.preventDefault();

    const rows = parseInt(document.getElementById('roomRows').value);
    const columns = parseInt(document.getElementById('roomColumns').value);
    const studentsPerBench = parseInt(document.getElementById('studentsPerBench').value);

    // Validate students per bench (max 4)
    if (studentsPerBench < 1 || studentsPerBench > 4) {
        alert('Students per bench must be between 1 and 4');
        return;
    }

    const room = {
        id: rooms.length + 1,
        name: document.getElementById('roomName').value,
        rows: rows,
        columns: columns,
        studentsPerBench: studentsPerBench,
        capacity: rows * columns * studentsPerBench,
        teacher: document.getElementById('teacherName').value
    };

    rooms.push(room);
    renderRoomsTable();

    // Reset form
    document.getElementById('roomForm').reset();
    // Set default value for students per bench
    document.getElementById('studentsPerBench').value = 1;
}

// Add new student manually
function addStudent(e) {
    e.preventDefault();

    const courseId = parseInt(document.getElementById('studentCourse').value);

    if (isNaN(courseId)) {
        alert('Please select a course');
        return;
    }

    const student = {
        id: document.getElementById('studentId').value,
        name: document.getElementById('studentName').value,
        email: document.getElementById('studentEmail').value,
        course: courseId
    };

    students.push(student);
    renderStudentsTable();

    // Reset form
    document.getElementById('studentForm').reset();
}

// Upload students from file
function uploadStudents(e) {
    e.preventDefault();

    const fileInput = document.getElementById('studentFile');
    const file = fileInput.files[0];

    if (!file) {
        alert('Please select a file to upload');
        return;
    }

    const reader = new FileReader();

    reader.onload = function(e) {
        try {
            let newStudents = [];

            if (file.name.endsWith('.csv')) {
                // Parse CSV
                const csvData = e.target.result;
                const lines = csvData.split('\n');

                // Skip header row
                for (let i = 1; i < lines.length; i++) {
                    if (lines[i].trim() === '') continue; // Skip empty lines

                    const values = lines[i].split(',');

                    // Check if we have the new template format (Serial No, Name, USN/Roll Number, Email, Course)
                    // or the old format (ID, Name, Email, Course)
                    if (values.length >= 5) {
                        // New format: Serial No, Name, USN/Roll Number, Email, Course
                        const [, name, id, email, courseId] = values;
                        if (id && name && email) {
                            const course = parseInt(courseId) || 1;
                            newStudents.push({ id, name, email, course });
                        }
                    } else if (values.length >= 3) {
                        // Old format: ID, Name, Email, Course
                        const [id, name, email, courseId] = values;
                        if (id && name && email) {
                            const course = parseInt(courseId) || 1;
                            newStudents.push({ id, name, email, course });
                        }
                    }
                }
            } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                // Parse Excel
                const workbook = XLSX.read(e.target.result, { type: 'array' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                // Skip header row
                for (let i = 1; i < data.length; i++) {
                    if (!data[i] || data[i].length === 0) continue; // Skip empty rows

                    // Check if we have the new template format (Serial No, Name, USN/Roll Number, Email, Course)
                    // or the old format (ID, Name, Email, Course)
                    if (data[i].length >= 5) {
                        // New format: Serial No, Name, USN/Roll Number, Email, Course
                        const [, name, id, email, courseId] = data[i];
                        if (id && name && email) {
                            const course = parseInt(courseId) || 1;
                            newStudents.push({ id, name, email, course });
                        }
                    } else if (data[i].length >= 3) {
                        // Old format: ID, Name, Email, Course
                        const [id, name, email, courseId] = data[i];
                        if (id && name && email) {
                            const course = parseInt(courseId) || 1;
                            newStudents.push({ id, name, email, course });
                        }
                    }
                }
            } else {
                alert('Unsupported file format. Please upload a CSV or Excel file.');
                return;
            }

            // Add new students to the list
            students = [...students, ...newStudents];
            renderStudentsTable();

            // Reset form
            document.getElementById('uploadForm').reset();
        } catch (error) {
            console.error('Error parsing file:', error);
            alert('Error parsing file. Please check the file format and try again.');
        }
    };

    if (file.name.endsWith('.xlsx')) {
        reader.readAsArrayBuffer(file);
    } else {
        reader.readAsText(file);
    }
}

// Generate seating arrangement
function generateSeatingArrangement(e) {
    e.preventDefault();

    const examId = parseInt(document.getElementById('seatingExam').value);
    const strategy = document.querySelector('input[name="strategy"]:checked').value;
    const numStudentsElement = document.getElementById('numStudents');

    if (isNaN(examId) || !numStudentsElement) {
        alert('Please select an exam first.');
        return;
    }

    // Get selected rooms
    const selectedRoomCheckboxes = document.querySelectorAll('input[name="selectedRooms"]:checked');
    if (selectedRoomCheckboxes.length === 0) {
        alert('Please select at least one room.');
        return;
    }

    const selectedRoomIds = Array.from(selectedRoomCheckboxes).map(checkbox => parseInt(checkbox.value));
    const selectedRooms = rooms.filter(room => selectedRoomIds.includes(room.id));

    if (selectedRooms.length === 0) {
        alert('Invalid room selection.');
        return;
    }

    const numStudents = parseInt(numStudentsElement.value);
    if (isNaN(numStudents) || numStudents <= 0) {
        alert('Please enter a valid number of students.');
        return;
    }

    const selectedExam = exams.find(e => e.id === examId);
    if (!selectedExam) {
        alert('Invalid exam selection.');
        return;
    }

    // Calculate total capacity of selected rooms
    const totalCapacity = selectedRooms.reduce((sum, room) => sum + room.capacity, 0);

    // Get available students based on selection
    let availableStudents = [];

    const courseSelection = document.getElementById('courseSelection').value;

    if (courseSelection === 'all') {
        // Use all students up to the specified number
        availableStudents = students.slice(0, numStudents);
    } else {
        // Get selected courses
        const selectedCourses = [];
        const checkboxes = document.querySelectorAll('input[name="selectedCourses"]:checked');

        checkboxes.forEach(checkbox => {
            selectedCourses.push(parseInt(checkbox.value));
        });

        if (selectedCourses.length === 0) {
            alert('Please select at least one course');
            return;
        }

        // Filter students by selected courses
        availableStudents = students.filter(student => selectedCourses.includes(student.course));
    }

    const totalStudents = availableStudents.length;

    // Check if we need additional rooms beyond what was selected
    if (totalStudents > totalCapacity) {
        const autoAssignRooms = document.getElementById('autoAssignRooms').checked;

        if (autoAssignRooms) {
            // Show room selection modal
            showRoomSelectionModal(examId, strategy, availableStudents, selectedRooms, totalStudents, totalCapacity);
            return;
        } else {
            alert(`The number of students (${totalStudents}) exceeds the total capacity of selected rooms (${totalCapacity}).
                  Please select more rooms or enable auto-assign rooms.`);
            return;
        }
    }

    // Distribute students across selected rooms
    let remainingStudents = [...availableStudents];
    let successCount = 0;

    for (const room of selectedRooms) {
        if (remainingStudents.length === 0) break;

        const studentsForThisRoom = remainingStudents.slice(0, room.capacity);
        remainingStudents = remainingStudents.slice(room.capacity);

        if (generateSeatingForRoom(examId, room.id, studentsForThisRoom, strategy)) {
            successCount++;
        }
    }

    if (successCount > 0) {
        alert(`Seating arrangements generated successfully for ${successCount} room(s)!`);
    } else {
        alert('Failed to generate seating arrangements.');
    }
}

// Show room selection modal for auto-assign
function showRoomSelectionModal(examId, strategy, availableStudents, selectedRooms, totalStudents, totalCapacity) {
    // Calculate how many additional students need to be accommodated
    const additionalStudentsNeeded = totalStudents - totalCapacity;

    // Find available rooms that weren't already selected
    const selectedRoomIds = selectedRooms.map(room => room.id);
    const availableRooms = rooms.filter(room => !selectedRoomIds.includes(room.id));

    if (availableRooms.length === 0) {
        alert('No additional rooms available. Please add more rooms or reduce the number of students.');
        return;
    }

    // Create modal
    const modal = document.createElement('div');
    modal.className = 'modal';

    let roomCheckboxes = '';
    availableRooms.forEach(room => {
        roomCheckboxes += `
            <div class="checkbox-item">
                <input type="checkbox" id="additional-room-${room.id}" name="additionalRooms" value="${room.id}">
                <label for="additional-room-${room.id}">${room.name} (Capacity: ${room.capacity})</label>
            </div>
        `;
    });

    modal.innerHTML = `
        <div class="modal-content" style="width: 600px; max-width: 90%;">
            <h3>Additional Rooms Needed</h3>
            <p>The number of students (${totalStudents}) exceeds the total capacity of selected rooms (${totalCapacity}).</p>
            <p>You need additional capacity for ${additionalStudentsNeeded} students.</p>

            <form id="additionalRoomsForm">
                <div class="form-group">
                    <label>Select Additional Rooms:</label>
                    <div class="checkbox-group" style="max-height: 200px; overflow-y: auto;">
                        ${roomCheckboxes}
                    </div>
                </div>

                <div class="form-group">
                    <label for="createNewRoom">Or Create a New Room:</label>
                    <div class="checkbox-inline">
                        <input type="checkbox" id="createNewRoom" onchange="toggleNewRoomFields()">
                        <label for="createNewRoom">Create a new room</label>
                    </div>
                </div>

                <div id="newRoomFields" style="display: none;">
                    <div class="form-group">
                        <label for="newRoomName">Room Name</label>
                        <input type="text" id="newRoomName">
                    </div>
                    <div class="form-group">
                        <label for="newRoomRows">Number of Rows</label>
                        <input type="number" id="newRoomRows" min="1" value="5">
                    </div>
                    <div class="form-group">
                        <label for="newRoomColumns">Number of Columns</label>
                        <input type="number" id="newRoomColumns" min="1" value="6">
                    </div>
                    <div class="form-group">
                        <label for="newRoomStudentsPerBench">Students Per Bench (max 4)</label>
                        <input type="number" id="newRoomStudentsPerBench" min="1" max="4" value="2">
                    </div>
                    <div class="form-group">
                        <label for="newRoomTeacher">Teacher In-charge</label>
                        <input type="text" id="newRoomTeacher">
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn">Proceed with Selected Rooms</button>
                    <button type="button" class="btn btn-cancel" onclick="closeModal()">Cancel</button>
                </div>
            </form>
        </div>
    `;

    document.body.appendChild(modal);

    // Add script to toggle new room fields
    window.toggleNewRoomFields = function() {
        const newRoomFields = document.getElementById('newRoomFields');
        newRoomFields.style.display = document.getElementById('createNewRoom').checked ? 'block' : 'none';
    };

    // Handle form submission
    document.getElementById('additionalRoomsForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const additionalRoomCheckboxes = document.querySelectorAll('input[name="additionalRooms"]:checked');
        const additionalRoomIds = Array.from(additionalRoomCheckboxes).map(checkbox => parseInt(checkbox.value));

        // Check if creating a new room
        const createNewRoom = document.getElementById('createNewRoom').checked;
        let newRoomId = null;

        if (createNewRoom) {
            const newRoomName = document.getElementById('newRoomName').value;
            const newRoomRows = parseInt(document.getElementById('newRoomRows').value);
            const newRoomColumns = parseInt(document.getElementById('newRoomColumns').value);
            const newRoomStudentsPerBench = parseInt(document.getElementById('newRoomStudentsPerBench').value);
            const newRoomTeacher = document.getElementById('newRoomTeacher').value;

            if (!newRoomName || isNaN(newRoomRows) || isNaN(newRoomColumns) || isNaN(newRoomStudentsPerBench)) {
                alert('Please fill in all fields for the new room.');
                return;
            }

            // Create new room
            const newRoom = {
                id: rooms.length + 1,
                name: newRoomName,
                rows: newRoomRows,
                columns: newRoomColumns,
                studentsPerBench: newRoomStudentsPerBench,
                capacity: newRoomRows * newRoomColumns * newRoomStudentsPerBench,
                teacher: newRoomTeacher
            };

            rooms.push(newRoom);
            renderRoomsTable();
            newRoomId = newRoom.id;

            // Add to selected rooms
            additionalRoomIds.push(newRoomId);
        }

        if (additionalRoomIds.length === 0 && !createNewRoom) {
            alert('Please select at least one additional room or create a new one.');
            return;
        }

        // Combine selected rooms with additional rooms
        const allSelectedRoomIds = [...selectedRooms.map(room => room.id), ...additionalRoomIds];
        const allSelectedRooms = rooms.filter(room => allSelectedRoomIds.includes(room.id));

        // Distribute students across all rooms
        let remainingStudents = [...availableStudents];
        let successCount = 0;

        for (const room of allSelectedRooms) {
            if (remainingStudents.length === 0) break;

            const studentsForThisRoom = remainingStudents.slice(0, room.capacity);
            remainingStudents = remainingStudents.slice(room.capacity);

            if (generateSeatingForRoom(examId, room.id, studentsForThisRoom, strategy)) {
                successCount++;
            }
        }

        closeModal();

        if (successCount > 0) {
            alert(`Seating arrangements generated successfully for ${successCount} room(s)!`);
        } else {
            alert('Failed to generate seating arrangements.');
        }
    });
}

// Render exams table
function renderExamsTable() {
    const tableBody = document.querySelector('#examsTable tbody');
    if (!tableBody) {
        console.error('Exams table body not found');
        return;
    }

    tableBody.innerHTML = '';

    exams.forEach(exam => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${exam.name}</td>
            <td>${exam.date}</td>
            <td>${exam.time}</td>
            <td>${exam.duration} minutes</td>
            <td>
                <button class="btn-edit" onclick="editExam(${exam.id})">Edit</button>
                <button class="btn-delete" onclick="deleteExam(${exam.id})">Delete</button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// Render rooms table
function renderRoomsTable() {
    const tableBody = document.querySelector('#roomsTable tbody');
    if (!tableBody) {
        console.error('Rooms table body not found');
        return;
    }

    tableBody.innerHTML = '';

    rooms.forEach(room => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${room.name}</td>
            <td>${room.rows}</td>
            <td>${room.columns}</td>
            <td>${room.studentsPerBench || 1}</td>
            <td>${room.capacity}</td>
            <td>${room.teacher || 'Not assigned'}</td>
            <td>
                <button class="btn-edit" onclick="editRoom(${room.id})">Edit</button>
                <button class="btn-delete" onclick="deleteRoom(${room.id})">Delete</button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// Render students table
function renderStudentsTable() {
    const tableBody = document.querySelector('#studentsTable tbody');
    if (!tableBody) {
        console.error('Students table body not found');
        return;
    }

    // Add delete selected button if it doesn't exist
    let deleteSelectedBtn = document.getElementById('deleteSelectedStudentsBtn');
    if (!deleteSelectedBtn) {
        const tableContainer = document.querySelector('#studentsTable').parentElement;
        deleteSelectedBtn = document.createElement('button');
        deleteSelectedBtn.id = 'deleteSelectedStudentsBtn';
        deleteSelectedBtn.className = 'btn btn-delete';
        deleteSelectedBtn.textContent = 'Delete Selected';
        deleteSelectedBtn.style.marginBottom = '10px';
        deleteSelectedBtn.onclick = deleteSelectedStudents;

        // Add select all checkbox
        const selectAllContainer = document.createElement('div');
        selectAllContainer.className = 'select-all-container';
        selectAllContainer.style.marginBottom = '10px';

        const selectAllCheckbox = document.createElement('input');
        selectAllCheckbox.type = 'checkbox';
        selectAllCheckbox.id = 'selectAllStudents';
        selectAllCheckbox.onchange = toggleSelectAllStudents;

        const selectAllLabel = document.createElement('label');
        selectAllLabel.htmlFor = 'selectAllStudents';
        selectAllLabel.textContent = 'Select All';
        selectAllLabel.style.marginLeft = '5px';

        selectAllContainer.appendChild(selectAllCheckbox);
        selectAllContainer.appendChild(selectAllLabel);

        // Insert before the table
        tableContainer.insertBefore(deleteSelectedBtn, tableContainer.firstChild);
        tableContainer.insertBefore(selectAllContainer, tableContainer.firstChild);
    }

    tableBody.innerHTML = '';

    students.forEach(student => {
        // Get course name
        const course = courses.find(c => c.id === student.course);
        const courseName = course ? course.name : 'Not assigned';

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <input type="checkbox" class="student-select" data-id="${student.id}">
            </td>
            <td>${student.id}</td>
            <td>${student.name}</td>
            <td>${student.email}</td>
            <td>${courseName}</td>
            <td>
                <button class="btn-edit" onclick="editStudent('${student.id}')">Edit</button>
                <button class="btn-delete" onclick="deleteStudent('${student.id}')">Delete</button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// Toggle select all students
function toggleSelectAllStudents() {
    const selectAll = document.getElementById('selectAllStudents').checked;
    document.querySelectorAll('.student-select').forEach(checkbox => {
        checkbox.checked = selectAll;
    });
}

// Delete selected students
function deleteSelectedStudents() {
    const selectedCheckboxes = document.querySelectorAll('.student-select:checked');

    if (selectedCheckboxes.length === 0) {
        alert('Please select at least one student to delete.');
        return;
    }

    if (confirm(`Are you sure you want to delete ${selectedCheckboxes.length} selected student(s)?`)) {
        const selectedIds = Array.from(selectedCheckboxes).map(checkbox => checkbox.getAttribute('data-id'));

        // Remove selected students
        students = students.filter(student => !selectedIds.includes(student.id));

        // Update the table
        renderStudentsTable();

        // Uncheck select all
        const selectAllCheckbox = document.getElementById('selectAllStudents');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
        }
    }
}

// Edit functions (placeholders)
function editExam(id) {
    alert('Edit exam functionality will be implemented here.');
}

function deleteExam(id) {
    if (confirm('Are you sure you want to delete this exam?')) {
        exams = exams.filter(exam => exam.id !== id);
        renderExamsTable();
    }
}

function editRoom(id) {
    alert('Edit room functionality will be implemented here.');
}

function deleteRoom(id) {
    if (confirm('Are you sure you want to delete this room?')) {
        rooms = rooms.filter(room => room.id !== id);
        renderRoomsTable();
    }
}

function editStudent(id) {
    const student = students.find(s => s.id === id);
    if (!student) {
        alert('Student not found');
        return;
    }

    // Create modal for editing
    const modal = document.createElement('div');
    modal.className = 'modal';

    // Create course options
    let courseOptions = '';
    courses.forEach(course => {
        const selected = course.id === student.course ? 'selected' : '';
        courseOptions += `<option value="${course.id}" ${selected}>${course.name}</option>`;
    });

    modal.innerHTML = `
        <div class="modal-content">
            <h3>Edit Student</h3>
            <form id="editStudentForm">
                <div class="form-group">
                    <label for="editStudentId">Roll Number/ID</label>
                    <input type="text" id="editStudentId" value="${student.id}" required>
                </div>
                <div class="form-group">
                    <label for="editStudentName">Full Name</label>
                    <input type="text" id="editStudentName" value="${student.name}" required>
                </div>
                <div class="form-group">
                    <label for="editStudentEmail">Email</label>
                    <input type="email" id="editStudentEmail" value="${student.email}" required>
                </div>
                <div class="form-group">
                    <label for="editStudentCourse">Course</label>
                    <select id="editStudentCourse" required>
                        <option value="">-- Select Course --</option>
                        ${courseOptions}
                    </select>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn">Save</button>
                    <button type="button" class="btn btn-cancel" onclick="closeModal()">Cancel</button>
                </div>
            </form>
        </div>
    `;

    document.body.appendChild(modal);

    // Handle form submission
    document.getElementById('editStudentForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const newId = document.getElementById('editStudentId').value;
        const newName = document.getElementById('editStudentName').value;
        const newEmail = document.getElementById('editStudentEmail').value;
        const newCourse = parseInt(document.getElementById('editStudentCourse').value);

        if (!newId || !newName || !newEmail || isNaN(newCourse)) {
            alert('Please fill in all fields correctly');
            return;
        }

        // Check if ID is changed and already exists
        if (newId !== student.id && students.some(s => s.id === newId)) {
            alert('A student with this ID already exists');
            return;
        }

        // Update student data
        const index = students.findIndex(s => s.id === student.id);
        if (index !== -1) {
            // If ID is changed, update references in seating arrangements
            if (newId !== student.id) {
                for (const key in seatingArrangements) {
                    const arrangement = seatingArrangements[key];
                    arrangement.seats.forEach(seat => {
                        if (seat.studentId === student.id) {
                            seat.studentId = newId;
                        }
                    });
                }
            }

            students[index] = {
                id: newId,
                name: newName,
                email: newEmail,
                course: newCourse
            };

            renderStudentsTable();
        }

        closeModal();
    });
}

function deleteStudent(id) {
    if (confirm('Are you sure you want to delete this student?')) {
        students = students.filter(student => student.id !== id);
        renderStudentsTable();
    }
}

// Load exams dropdown
function loadExamsDropdown(selectId) {
    const select = document.getElementById(selectId);
    select.innerHTML = '<option value="">Select an exam</option>';

    exams.forEach(exam => {
        const option = document.createElement('option');
        option.value = exam.id;
        option.textContent = exam.name;
        select.appendChild(option);
    });
}

// Load rooms dropdown
function loadRoomsDropdown(selectId) {
    // For the visualization section, use a regular dropdown
    if (selectId === 'visualRoom') {
        const select = document.getElementById(selectId);
        select.innerHTML = '<option value="">Select a room</option>';

        rooms.forEach(room => {
            const option = document.createElement('option');
            option.value = room.id;
            option.textContent = room.name;
            select.appendChild(option);
        });
        return;
    }

    // For the seating section, use checkboxes
    const container = document.getElementById('roomsCheckboxes');
    if (!container) return;

    container.innerHTML = '';

    // Add select all checkbox
    const selectAllDiv = document.createElement('div');
    selectAllDiv.className = 'checkbox-item';

    const selectAllCheckbox = document.createElement('input');
    selectAllCheckbox.type = 'checkbox';
    selectAllCheckbox.id = 'selectAllRooms';
    selectAllCheckbox.name = 'selectAllRooms';
    selectAllCheckbox.onchange = function() {
        const isChecked = this.checked;
        document.querySelectorAll('input[name="selectedRooms"]').forEach(checkbox => {
            checkbox.checked = isChecked;
        });
    };

    const selectAllLabel = document.createElement('label');
    selectAllLabel.htmlFor = 'selectAllRooms';
    selectAllLabel.textContent = 'Select All Rooms';

    selectAllDiv.appendChild(selectAllCheckbox);
    selectAllDiv.appendChild(selectAllLabel);
    container.appendChild(selectAllDiv);

    // Add separator
    const separator = document.createElement('hr');
    separator.style.margin = '10px 0';
    container.appendChild(separator);

    // Add room checkboxes
    rooms.forEach(room => {
        const div = document.createElement('div');
        div.className = 'checkbox-item';

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.value = room.id;
        checkbox.name = 'selectedRooms';
        checkbox.id = `room-${room.id}`;

        const label = document.createElement('label');
        label.htmlFor = `room-${room.id}`;
        label.textContent = `${room.name} (Capacity: ${room.capacity})`;

        div.appendChild(checkbox);
        div.appendChild(label);
        container.appendChild(div);
    });
}

// Load courses dropdown
function loadCoursesDropdown(selectId) {
    const select = document.getElementById(selectId);
    if (!select) return;

    select.innerHTML = '<option value="">-- Select Course --</option>';

    courses.forEach(course => {
        const option = document.createElement('option');
        option.value = course.id;
        option.textContent = course.name;
        select.appendChild(option);
    });
}

// Load course checkboxes
function loadCourseCheckboxes() {
    const container = document.getElementById('courseCheckboxes');
    if (!container) return;

    container.innerHTML = '';

    courses.forEach(course => {
        const label = document.createElement('label');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.value = course.id;
        checkbox.name = 'selectedCourses';
        checkbox.id = `course-${course.id}`;

        label.appendChild(checkbox);
        label.appendChild(document.createTextNode(` ${course.name}`));

        container.appendChild(label);
    });
}

// Load visualization of seating arrangement
function loadVisualization() {
    const examId = parseInt(document.getElementById('visualExam').value);
    const roomId = parseInt(document.getElementById('visualRoom').value);

    if (isNaN(examId) || isNaN(roomId)) {
        return;
    }

    const seatingKey = `exam${examId}_room${roomId}`;
    const seatingData = seatingArrangements[seatingKey];

    if (!seatingData) {
        document.getElementById('roomVisualization').innerHTML = '<p>No seating arrangement found for this exam and room.</p>';
        return;
    }

    const selectedRoom = rooms.find(r => r.id === roomId);
    if (!selectedRoom) {
        return;
    }

    // Create visualization
    const visualContainer = document.getElementById('roomVisualization');
    visualContainer.innerHTML = '';

    // Add room info
    const roomInfo = document.createElement('div');
    roomInfo.className = 'room-info';

    // Get exam details
    const selectedExam = exams.find(e => e.id === examId);

    // Count students by course
    const courseStudentCounts = {};
    let totalStudentsInRoom = 0;

    seatingData.seats.forEach(seat => {
        const student = students.find(s => s.id === seat.studentId);
        if (student) {
            totalStudentsInRoom++;
            if (student.course) {
                courseStudentCounts[student.course] = (courseStudentCounts[student.course] || 0) + 1;
            }
        }
    });

    // Create course distribution HTML
    let courseDistributionHTML = '';
    if (Object.keys(courseStudentCounts).length > 0) {
        courseDistributionHTML = '<div class="course-distribution"><h4>Course Distribution</h4><ul>';
        for (const courseId in courseStudentCounts) {
            const course = courses.find(c => c.id === parseInt(courseId));
            if (course) {
                courseDistributionHTML += `<li>${course.name}: ${courseStudentCounts[courseId]} students</li>`;
            }
        }
        courseDistributionHTML += '</ul></div>';
    }

    // Count students by set if using set-wise strategy
    let setDistributionHTML = '';
    if (seatingData.strategy === 'setwise') {
        const setCounts = { 'A': 0, 'B': 0, 'C': 0, 'D': 0 };

        seatingData.seats.forEach(seat => {
            if (seat.set && setCounts.hasOwnProperty(seat.set)) {
                setCounts[seat.set]++;
            }
        });

        setDistributionHTML = '<div class="set-distribution"><h4>Set Distribution</h4><ul>';
        for (const set in setCounts) {
            if (setCounts[set] > 0) {
                setDistributionHTML += `<li>Set ${set}: ${setCounts[set]} students</li>`;
            }
        }
        setDistributionHTML += '</ul></div>';
    }

    roomInfo.innerHTML = `
        <h3>Room: ${selectedRoom.name}</h3>
        <p><strong>Exam:</strong> ${selectedExam ? selectedExam.name : 'Unknown'}</p>
        <p><strong>Date:</strong> ${selectedExam ? selectedExam.date : 'Unknown'}</p>
        <p><strong>Time:</strong> ${selectedExam ? selectedExam.time : 'Unknown'}</p>
        <p><strong>Teacher:</strong> ${selectedRoom.teacher || 'Not assigned'}</p>
        <p><strong>Arrangement Strategy:</strong> ${seatingData.strategy || 'Random'}</p>
        <p><strong>Total Students:</strong> ${totalStudentsInRoom} / ${selectedRoom.capacity} (${Math.round(totalStudentsInRoom/selectedRoom.capacity*100)}% capacity)</p>
        ${courseDistributionHTML}
        ${setDistributionHTML}
    `;
    visualContainer.appendChild(roomInfo);

    // Create seating grid
    const grid = document.createElement('div');
    grid.className = 'seating-grid';
    grid.style.gridTemplateColumns = `repeat(${selectedRoom.columns}, 1fr)`;

    // Create a 2D array to represent the room
    const roomGrid = Array(selectedRoom.rows).fill().map(() => Array(selectedRoom.columns).fill(null));

    // Fill in the seats with student data
    seatingData.seats.forEach(seat => {
        if (seat.row < selectedRoom.rows && seat.col < selectedRoom.columns) {
            roomGrid[seat.row][seat.col] = seat;
        }
    });

    // Create bench elements
    for (let row = 0; row < selectedRoom.rows; row++) {
        for (let col = 0; col < selectedRoom.columns; col++) {
            const bench = document.createElement('div');
            bench.className = 'bench';

            // Get all students assigned to this bench
            const benchStudents = [];
            const maxStudentsPerBench = selectedRoom.studentsPerBench || 1;

            // Find students assigned to this bench
            for (let i = 0; i < seatingData.seats.length; i++) {
                const seat = seatingData.seats[i];
                if (seat.row === row && seat.col === col) {
                    const student = students.find(s => s.id === seat.studentId);
                    if (student) {
                        benchStudents.push({
                            id: student.id,
                            name: student.name,
                            position: seat.position || benchStudents.length,
                            set: seat.set
                        });
                    }
                }
            }

            // Sort students by position
            benchStudents.sort((a, b) => a.position - b.position);

            // Create bench header
            const benchHeader = document.createElement('div');
            benchHeader.className = 'bench-header';
            benchHeader.innerHTML = `Bench R${row+1}C${col+1}`;
            bench.appendChild(benchHeader);

            // Create student seats within the bench
            const benchContent = document.createElement('div');
            benchContent.className = 'bench-content';

            // Add student seats
            for (let i = 0; i < maxStudentsPerBench; i++) {
                const seatElement = document.createElement('div');
                seatElement.className = 'student-seat';

                if (i < benchStudents.length) {
                    // Occupied seat
                    const student = benchStudents[i];
                    seatElement.classList.add('occupied');

                    if (student.set) {
                        seatElement.classList.add(`set-${student.set.toLowerCase()}`);
                    }

                    seatElement.innerHTML = `
                        <div class="seat-position">Position ${i+1}</div>
                        <div class="student-id">${student.id}</div>
                        <div class="student-name">${student.name}</div>
                        ${student.set ? `<div class="student-set">Set ${student.set}</div>` : ''}
                    `;

                    // Add edit functionality for admin
                    seatElement.addEventListener('click', function() {
                        editSeatAssignment(examId, roomId, row, col, student.id, i);
                    });
                } else {
                    // Empty seat
                    seatElement.classList.add('empty');
                    seatElement.innerHTML = `<div class="seat-position">Position ${i+1}</div><div class="empty-text">Empty</div>`;

                    // Add ability to assign a student to an empty seat
                    seatElement.addEventListener('click', function() {
                        assignStudentToSeat(examId, roomId, row, col, i);
                    });
                }

                benchContent.appendChild(seatElement);
            }

            bench.appendChild(benchContent);
            grid.appendChild(bench);
        }
    }

    visualContainer.appendChild(grid);

    // Add legend for set-wise arrangement
    if (seatingData.strategy === 'setwise') {
        const legend = document.createElement('div');
        legend.className = 'seating-legend';

        // Count students by set
        const setCounts = { 'A': 0, 'B': 0, 'C': 0, 'D': 0 };

        seatingData.seats.forEach(seat => {
            if (seat.set && setCounts.hasOwnProperty(seat.set)) {
                setCounts[seat.set]++;
            }
        });

        // Create set distribution HTML
        let setDistributionHTML = '<div class="set-distribution-table"><table><tr><th>Set</th><th>Count</th><th>Color</th></tr>';
        for (const set in setCounts) {
            if (setCounts[set] > 0) {
                setDistributionHTML += `
                    <tr>
                        <td>Set ${set}</td>
                        <td>${setCounts[set]} students</td>
                        <td><span class="legend-color set-${set.toLowerCase()}"></span></td>
                    </tr>
                `;
            }
        }
        setDistributionHTML += '</table></div>';

        legend.innerHTML = `
            <h3>Set Legend</h3>
            <div class="legend-items">
                <div class="legend-item"><span class="legend-color set-a"></span> Set A (Red)</div>
                <div class="legend-item"><span class="legend-color set-b"></span> Set B (Green)</div>
                <div class="legend-item"><span class="legend-color set-c"></span> Set C (Blue)</div>
                <div class="legend-item"><span class="legend-color set-d"></span> Set D (Yellow)</div>
            </div>

            <h4>Set Distribution</h4>
            ${setDistributionHTML}

            <div class="bench-pattern">
                <h4>Set Arrangement Rules</h4>
                <p>The set-wise arrangement follows these rules:</p>
                <ul>
                    <li>No student sits next to another student from the same set</li>
                    <li>Sets are distributed to avoid having the same set in adjacent benches (including diagonals)</li>
                    <li>When there are at least 2 students per bench, no similar sets are placed in the same row or column</li>
                    <li>Each bench can have students from different sets</li>
                </ul>
                <div class="pattern-example">
                    <h5>Example Pattern:</h5>
                    <div class="pattern-grid">
                        <div class="pattern-row">
                            <div class="pattern-bench">
                                <span class="pattern-seat set-a">A</span>
                            </div>
                            <div class="pattern-bench">
                                <span class="pattern-seat set-b">B</span>
                            </div>
                            <div class="pattern-bench">
                                <span class="pattern-seat set-c">C</span>
                            </div>
                        </div>
                        <div class="pattern-row">
                            <div class="pattern-bench">
                                <span class="pattern-seat set-b">B</span>
                            </div>
                            <div class="pattern-bench">
                                <span class="pattern-seat set-c">C</span>
                            </div>
                            <div class="pattern-bench">
                                <span class="pattern-seat set-d">D</span>
                            </div>
                        </div>
                        <div class="pattern-row">
                            <div class="pattern-bench">
                                <span class="pattern-seat set-c">C</span>
                            </div>
                            <div class="pattern-bench">
                                <span class="pattern-seat set-d">D</span>
                            </div>
                            <div class="pattern-bench">
                                <span class="pattern-seat set-a">A</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <p class="legend-note">Sets are arranged to ensure no similar sets are adjacent to each other, including diagonally adjacent benches.</p>
        `;
        visualContainer.appendChild(legend);
    }
}

// Edit seat assignment
function editSeatAssignment(examId, roomId, row, col, currentStudentId, position = 0) {
    const seatingKey = `exam${examId}_room${roomId}`;
    const seatingData = seatingArrangements[seatingKey];

    if (!seatingData) return;

    // Create a list of students to choose from
    let studentOptions = '';
    students.forEach(student => {
        const selected = student.id === currentStudentId ? 'selected' : '';
        studentOptions += `<option value="${student.id}" ${selected}>${student.name} (${student.id})</option>`;
    });

    // Add option to remove student
    studentOptions += `<option value="remove">-- Remove Student --</option>`;

    // Create modal for editing
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <h3>Edit Seat Assignment</h3>
            <p>Bench: Row ${row+1}, Column ${col+1}, Position ${position+1}</p>
            <form id="editSeatForm">
                <div class="form-group">
                    <label for="studentSelect">Assign Student:</label>
                    <select id="studentSelect">
                        ${studentOptions}
                    </select>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn">Save</button>
                    <button type="button" class="btn btn-cancel" onclick="closeModal()">Cancel</button>
                </div>
            </form>
        </div>
    `;

    document.body.appendChild(modal);

    // Handle form submission
    document.getElementById('editSeatForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const selectedStudentId = document.getElementById('studentSelect').value;

        if (selectedStudentId === 'remove') {
            // Remove student from seat
            const seatIndex = seatingData.seats.findIndex(s =>
                s.row === row && s.col === col && s.studentId === currentStudentId);
            if (seatIndex !== -1) {
                seatingData.seats.splice(seatIndex, 1);
            }
        } else {
            // Update or add seat assignment
            const seatIndex = seatingData.seats.findIndex(s =>
                s.row === row && s.col === col && s.studentId === currentStudentId);
            if (seatIndex !== -1) {
                seatingData.seats[seatIndex].studentId = selectedStudentId;
            } else {
                seatingData.seats.push({
                    row,
                    col,
                    studentId: selectedStudentId,
                    position: position
                });
            }
        }

        // Close modal and refresh visualization
        closeModal();
        loadVisualization();
    });
}

// Assign student to empty seat
function assignStudentToSeat(examId, roomId, row, col, position = 0) {
    const seatingKey = `exam${examId}_room${roomId}`;
    const seatingData = seatingArrangements[seatingKey];

    if (!seatingData) return;

    // Get the selected room
    const selectedRoom = rooms.find(r => r.id === roomId);
    if (!selectedRoom) return;

    // Check if the bench already has maximum students
    const benchStudents = seatingData.seats.filter(s => s.row === row && s.col === col);
    if (benchStudents.length >= (selectedRoom.studentsPerBench || 1)) {
        alert('This bench already has the maximum number of students allowed.');
        return;
    }

    // Create a list of students to choose from
    let studentOptions = '<option value="">-- Select Student --</option>';

    // Get list of students already assigned in this room
    const assignedStudentIds = seatingData.seats.map(seat => seat.studentId);

    // Show only unassigned students
    students.forEach(student => {
        if (!assignedStudentIds.includes(student.id)) {
            studentOptions += `<option value="${student.id}">${student.name} (${student.id})</option>`;
        }
    });

    // Create modal for assigning
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <h3>Assign Student to Bench</h3>
            <p>Bench: Row ${row+1}, Column ${col+1}, Position ${position+1}</p>
            <form id="assignSeatForm">
                <div class="form-group">
                    <label for="studentSelect">Select Student:</label>
                    <select id="studentSelect" required>
                        ${studentOptions}
                    </select>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn">Assign</button>
                    <button type="button" class="btn btn-cancel" onclick="closeModal()">Cancel</button>
                </div>
            </form>
        </div>
    `;

    document.body.appendChild(modal);

    // Handle form submission
    document.getElementById('assignSeatForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const selectedStudentId = document.getElementById('studentSelect').value;

        if (selectedStudentId) {
            // Add seat assignment
            seatingData.seats.push({
                row,
                col,
                studentId: selectedStudentId,
                position: position
            });

            // Close modal and refresh visualization
            closeModal();
            loadVisualization();
        }
    });
}

// Close modal
function closeModal() {
    const modal = document.querySelector('.modal');
    if (modal) {
        document.body.removeChild(modal);
    }
}

// Find an available room with sufficient capacity
function findAvailableRoom(minCapacity) {
    // Filter rooms by capacity
    const availableRooms = rooms.filter(room => room.capacity >= minCapacity);

    if (availableRooms.length === 0) {
        return null;
    }

    // Return the first available room
    return availableRooms[0].id;
}

// Generate seating for a specific room
function generateSeatingForRoom(examId, roomId, availableStudents, strategy) {
    if (!availableStudents || availableStudents.length === 0) {
        return false;
    }

    const selectedRoom = rooms.find(r => r.id === roomId);
    if (!selectedRoom) {
        alert('Invalid room selection.');
        return false;
    }

    // Create seats array
    const seats = [];
    const studentsPerBench = selectedRoom.studentsPerBench || 1;

    switch(strategy) {
        case 'random':
            // Shuffle students randomly
            const shuffledStudents = [...availableStudents].sort(() => Math.random() - 0.5);

            // Assign students to benches
            for (let i = 0; i < shuffledStudents.length; i++) {
                const benchIndex = Math.floor(i / studentsPerBench);
                const positionInBench = i % studentsPerBench;

                const row = Math.floor(benchIndex / selectedRoom.columns);
                const col = benchIndex % selectedRoom.columns;

                if (row < selectedRoom.rows && col < selectedRoom.columns) {
                    seats.push({
                        row,
                        col,
                        studentId: shuffledStudents[i].id,
                        position: positionInBench
                    });
                }
            }
            break;

        case 'roll':
            // Sort by roll number/ID
            const sortedByRoll = [...availableStudents].sort((a, b) => a.id.localeCompare(b.id));

            // Assign students to benches
            for (let i = 0; i < sortedByRoll.length; i++) {
                const benchIndex = Math.floor(i / studentsPerBench);
                const positionInBench = i % studentsPerBench;

                const row = Math.floor(benchIndex / selectedRoom.columns);
                const col = benchIndex % selectedRoom.columns;

                if (row < selectedRoom.rows && col < selectedRoom.columns) {
                    seats.push({
                        row,
                        col,
                        studentId: sortedByRoll[i].id,
                        position: positionInBench
                    });
                }
            }
            break;

        case 'zigzag':
            // Zig-zag pattern (alternating direction in each row)
            for (let i = 0; i < availableStudents.length; i++) {
                const benchIndex = Math.floor(i / studentsPerBench);
                const positionInBench = i % studentsPerBench;

                let row = Math.floor(benchIndex / selectedRoom.columns);
                let col;

                // If even row, go left to right, if odd row, go right to left
                if (row % 2 === 0) {
                    col = benchIndex % selectedRoom.columns;
                } else {
                    col = selectedRoom.columns - 1 - (benchIndex % selectedRoom.columns);
                }

                if (row < selectedRoom.rows && col < selectedRoom.columns) {
                    seats.push({
                        row,
                        col,
                        studentId: availableStudents[i].id,
                        position: positionInBench
                    });
                }
            }
            break;

        case 'setwise':
            // Divide students into 4 sets
            const setSize = Math.ceil(availableStudents.length / 4);
            const setA = availableStudents.slice(0, setSize);
            const setB = availableStudents.slice(setSize, setSize * 2);
            const setC = availableStudents.slice(setSize * 2, setSize * 3);
            const setD = availableStudents.slice(setSize * 3);

            // Group students by set
            const setGroups = {
                'A': setA,
                'B': setB,
                'C': setC,
                'D': setD
            };

            // Get total number of students
            const totalStudents = availableStudents.length;

            // Create a 2D array to track set assignments
            const setAssignments = Array(selectedRoom.rows).fill().map(() =>
                Array(selectedRoom.columns).fill().map(() =>
                    Array(studentsPerBench).fill(null)
                )
            );

            // Define the set order (we'll use this to determine which set to place next)
            const setOrder = ['A', 'B', 'C', 'D'];

            // Keep track of how many students we've assigned from each set
            let setCounters = { 'A': 0, 'B': 0, 'C': 0, 'D': 0 };

            // Function to check if a set can be placed at a specific position
            function canPlaceSet(row, col, position, set) {
                // Check adjacent positions within the same bench
                for (let p = 0; p < studentsPerBench; p++) {
                    if (p !== position && setAssignments[row][col][p] === set) {
                        return false; // Same set already exists in this bench
                    }
                }

                // Check all adjacent benches (including diagonals)
                for (let dr = -1; dr <= 1; dr++) {
                    for (let dc = -1; dc <= 1; dc++) {
                        // Skip the current bench
                        if (dr === 0 && dc === 0) continue;

                        const newRow = row + dr;
                        const newCol = col + dc;

                        // Check if the adjacent bench is within bounds
                        if (newRow >= 0 && newRow < selectedRoom.rows &&
                            newCol >= 0 && newCol < selectedRoom.columns) {

                            // Check if any position in the adjacent bench has the same set
                            for (let p = 0; p < studentsPerBench; p++) {
                                if (setAssignments[newRow][newCol][p] === set) {
                                    return false; // Same set exists in adjacent bench
                                }
                            }
                        }
                    }
                }

                // If there are at least 2 students per bench, check entire row and column
                if (studentsPerBench >= 2) {
                    // Check same row
                    for (let c = 0; c < selectedRoom.columns; c++) {
                        if (c !== col) {
                            for (let p = 0; p < studentsPerBench; p++) {
                                if (setAssignments[row][c][p] === set) {
                                    return false; // Same set exists in the same row
                                }
                            }
                        }
                    }

                    // Check same column
                    for (let r = 0; r < selectedRoom.rows; r++) {
                        if (r !== row) {
                            for (let p = 0; p < studentsPerBench; p++) {
                                if (setAssignments[r][col][p] === set) {
                                    return false; // Same set exists in the same column
                                }
                            }
                        }
                    }
                }

                return true; // Safe to place this set here
            }

            // Assign students to benches using a greedy approach
            for (let row = 0; row < selectedRoom.rows; row++) {
                for (let col = 0; col < selectedRoom.columns; col++) {
                    for (let position = 0; position < studentsPerBench; position++) {
                        // Try each set in order until we find one that can be placed
                        let setPlaced = false;

                        // Try each set in our preferred order
                        for (const set of setOrder) {
                            // Check if we have students left in this set and if it's safe to place
                            if (setCounters[set] < setGroups[set].length && canPlaceSet(row, col, position, set)) {
                                // Place this set
                                setAssignments[row][col][position] = set;

                                // Get the student from this set
                                const student = setGroups[set][setCounters[set]];
                                setCounters[set]++;

                                // Add to seats
                                seats.push({
                                    row,
                                    col,
                                    studentId: student.id,
                                    set: set,
                                    position: position
                                });

                                setPlaced = true;
                                break;
                            }
                        }

                        // If we couldn't place any set, try again without the adjacency constraint
                        if (!setPlaced) {
                            for (const set of setOrder) {
                                if (setCounters[set] < setGroups[set].length) {
                                    // Place this set anyway
                                    setAssignments[row][col][position] = set;

                                    // Get the student from this set
                                    const student = setGroups[set][setCounters[set]];
                                    setCounters[set]++;

                                    // Add to seats
                                    seats.push({
                                        row,
                                        col,
                                        studentId: student.id,
                                        set: set,
                                        position: position
                                    });

                                    break;
                                }
                            }
                        }

                        // If we've assigned all students, break out
                        if (seats.length >= totalStudents) {
                            break;
                        }
                    }

                    // If we've assigned all students, break out
                    if (seats.length >= totalStudents) {
                        break;
                    }
                }

                // If we've assigned all students, break out
                if (seats.length >= totalStudents) {
                    break;
                }
            }
            break;

        case 'fronttoback':
            // Front to back arrangement
            for (let i = 0; i < availableStudents.length; i++) {
                const benchIndex = Math.floor(i / studentsPerBench);
                const positionInBench = i % studentsPerBench;

                const row = Math.floor(benchIndex / selectedRoom.columns);
                const col = benchIndex % selectedRoom.columns;

                if (row < selectedRoom.rows && col < selectedRoom.columns) {
                    seats.push({
                        row,
                        col,
                        studentId: availableStudents[i].id,
                        position: positionInBench
                    });
                }
            }
            break;

        case 'backtofront':
            // Back to front arrangement
            for (let i = 0; i < availableStudents.length; i++) {
                const benchIndex = Math.floor(i / studentsPerBench);
                const positionInBench = i % studentsPerBench;

                // Calculate position from back to front
                const totalBenches = selectedRoom.rows * selectedRoom.columns;
                const reverseBenchIndex = totalBenches - benchIndex - 1;
                const row = Math.floor(reverseBenchIndex / selectedRoom.columns);
                const col = reverseBenchIndex % selectedRoom.columns;

                if (row >= 0 && row < selectedRoom.rows && col >= 0 && col < selectedRoom.columns) {
                    seats.push({
                        row,
                        col,
                        studentId: availableStudents[i].id,
                        position: positionInBench
                    });
                }
            }
            break;

        case 'course':
            // By course (assuming we have course info, otherwise just use alphabetical order)
            const sortedByName = [...availableStudents].sort((a, b) => a.name.localeCompare(b.name));

            for (let i = 0; i < sortedByName.length; i++) {
                const benchIndex = Math.floor(i / studentsPerBench);
                const positionInBench = i % studentsPerBench;

                const row = Math.floor(benchIndex / selectedRoom.columns);
                const col = benchIndex % selectedRoom.columns;

                if (row < selectedRoom.rows && col < selectedRoom.columns) {
                    seats.push({
                        row,
                        col,
                        studentId: sortedByName[i].id,
                        position: positionInBench
                    });
                }
            }
            break;

        default:
            // Default to random if strategy not recognized
            const defaultStudents = [...availableStudents].sort(() => Math.random() - 0.5);

            for (let i = 0; i < defaultStudents.length; i++) {
                const benchIndex = Math.floor(i / studentsPerBench);
                const positionInBench = i % studentsPerBench;

                const row = Math.floor(benchIndex / selectedRoom.columns);
                const col = benchIndex % selectedRoom.columns;

                if (row < selectedRoom.rows && col < selectedRoom.columns) {
                    seats.push({
                        row,
                        col,
                        studentId: defaultStudents[i].id,
                        position: positionInBench
                    });
                }
            }
    }

    // Save the seating arrangement
    seatingArrangements[`exam${examId}_room${roomId}`] = {
        examId,
        roomId,
        seats,
        strategy
    };

    return true;
}

// Export seating arrangement to PDF or Excel
function exportSeating(format) {
    const examId = parseInt(document.getElementById('seatingExam').value);
    const roomIdElement = document.getElementById('seatingRoom');

    if (isNaN(examId) || !roomIdElement) {
        alert('Please select an exam and generate a seating arrangement first.');
        return;
    }

    const roomId = parseInt(roomIdElement.value);

    if (isNaN(roomId)) {
        alert('Please select a room.');
        return;
    }

    const seatingKey = `exam${examId}_room${roomId}`;
    const seatingData = seatingArrangements[seatingKey];

    if (!seatingData) {
        alert('No seating arrangement found. Please generate a seating arrangement first.');
        return;
    }

    const selectedExam = exams.find(e => e.id === examId);
    const selectedRoom = rooms.find(r => r.id === roomId);

    if (!selectedExam || !selectedRoom) {
        alert('Invalid exam or room selection.');
        return;
    }

    if (format === 'pdf') {
        // Export to PDF
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Add title
        doc.setFontSize(16);
        doc.text(`Exam Seating Arrangement: ${selectedExam.name}`, 10, 10);

        // Add exam details
        doc.setFontSize(12);
        doc.text(`Date: ${selectedExam.date}`, 10, 20);
        doc.text(`Time: ${selectedExam.time}`, 10, 25);
        doc.text(`Duration: ${selectedExam.duration} minutes`, 10, 30);
        doc.text(`Room: ${selectedRoom.name}`, 10, 35);
        doc.text(`Teacher: ${selectedRoom.teacher || 'Not assigned'}`, 10, 40);
        doc.text(`Strategy: ${seatingData.strategy || 'Random'}`, 10, 45);

        // Add seating table
        doc.setFontSize(14);
        doc.text('Seating Arrangement', 10, 55);

        // Table headers
        doc.setFontSize(10);
        doc.text('Row', 10, 65);
        doc.text('Column', 30, 65);
        doc.text('Student ID', 50, 65);
        doc.text('Student Name', 90, 65);
        doc.text('Set', 150, 65);

        // Table content
        let y = 70;
        seatingData.seats.forEach(seat => {
            const student = students.find(s => s.id === seat.studentId);
            if (student) {
                doc.text(`${seat.row + 1}`, 10, y);
                doc.text(`${seat.col + 1}`, 30, y);
                doc.text(student.id, 50, y);
                doc.text(student.name, 90, y);
                if (seat.set) {
                    doc.text(seat.set, 150, y);
                }
                y += 5;

                // Add new page if needed
                if (y > 280) {
                    doc.addPage();
                    y = 20;
                }
            }
        });

        // Add set totals if using set-wise strategy
        if (seatingData.strategy === 'setwise') {
            // Count students by set
            const setCounts = { 'A': 0, 'B': 0, 'C': 0, 'D': 0 };

            seatingData.seats.forEach(seat => {
                if (seat.set && setCounts.hasOwnProperty(seat.set)) {
                    setCounts[seat.set]++;
                }
            });

            // Add set totals
            const setTotalsY = y + 10;
            doc.setFontSize(12);
            doc.text('Set Totals:', 10, setTotalsY);

            let setTotalY = setTotalsY + 5;
            for (const set in setCounts) {
                if (setCounts[set] > 0) {
                    doc.text(`Set ${set}: ${setCounts[set]} students`, 10, setTotalY);
                    setTotalY += 5;
                }
            }
        }

        // Save the PDF
        doc.save(`seating_${selectedExam.name}_${selectedRoom.name}.pdf`);

    } else if (format === 'excel') {
        // Export to Excel
        const XLSX = window.XLSX;

        // Prepare data
        const data = [
            ['Exam Seating Arrangement'],
            [`Exam: ${selectedExam.name}`],
            [`Date: ${selectedExam.date}`],
            [`Time: ${selectedExam.time}`],
            [`Duration: ${selectedExam.duration} minutes`],
            [`Room: ${selectedRoom.name}`],
            [`Teacher: ${selectedRoom.teacher || 'Not assigned'}`],
            [`Strategy: ${seatingData.strategy || 'Random'}`],
            [''],
            ['Row', 'Column', 'Student ID', 'Student Name', 'Set']
        ];

        // Add seating data
        seatingData.seats.forEach(seat => {
            const student = students.find(s => s.id === seat.studentId);
            if (student) {
                data.push([
                    seat.row + 1,
                    seat.col + 1,
                    student.id,
                    student.name,
                    seat.set || ''
                ]);
            }
        });

        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet(data);

        // Create workbook
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Seating Arrangement');

        // Save the Excel file
        XLSX.writeFile(wb, `seating_${selectedExam.name}_${selectedRoom.name}.xlsx`);
    }
}

// Print visualization
function printVisualization() {
    const visualContainer = document.getElementById('roomVisualization');
    if (!visualContainer) {
        alert('No visualization to print.');
        return;
    }

    // Create a new window for printing
    const printWindow = window.open('', '_blank');

    // Get the exam and room details
    const examId = parseInt(document.getElementById('visualExam').value);
    const roomId = parseInt(document.getElementById('visualRoom').value);

    if (isNaN(examId) || isNaN(roomId)) {
        alert('Please select an exam and room first.');
        printWindow.close();
        return;
    }

    const selectedExam = exams.find(e => e.id === examId);
    const selectedRoom = rooms.find(r => r.id === roomId);

    if (!selectedExam || !selectedRoom) {
        alert('Invalid exam or room selection.');
        printWindow.close();
        return;
    }

    // Create print content
    printWindow.document.write(`
        <html>
        <head>
            <title>Exam Seating Visualization</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                h1 { text-align: center; }
                .details { margin-bottom: 20px; }
                .details p { margin: 5px 0; }
                .visualization { margin-top: 20px; }
                .room-info { margin-bottom: 20px; padding: 10px; background: #e9f5ff; border-radius: 5px; }
                .seating-grid { display: grid; gap: 10px; margin-top: 20px; }
                .bench { background: #f5f5f5; border-radius: 5px; padding: 10px; margin-bottom: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
                .bench-header { font-weight: bold; padding: 5px; background: #e0e0e0; border-radius: 3px; margin-bottom: 10px; text-align: center; }
                .bench-content { display: flex; flex-direction: row; gap: 10px; justify-content: space-between; }
                .student-seat { background: #e0e0e0; border-radius: 5px; padding: 10px; text-align: center; min-height: 80px; min-width: 100px; flex: 1; }
                .student-seat.occupied { background: #b8e0d2; }
                .student-seat.empty { background: #f0f0f0; }
                .seat-position { font-size: 12px; color: #666; margin-bottom: 5px; font-weight: bold; }
                .student-id { font-weight: bold; margin-bottom: 3px; }
                .student-name { font-size: 12px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
                .student-set { font-size: 12px; font-weight: bold; margin-top: 3px; padding: 2px 5px; border-radius: 3px; display: inline-block; }
                .student-seat.set-a { background: #e57373; border: 2px solid #c62828; }
                .student-seat.set-b { background: #81c784; border: 2px solid #2e7d32; }
                .student-seat.set-c { background: #64b5f6; border: 2px solid #1565c0; }
                .student-seat.set-d { background: #fff176; border: 2px solid #f9a825; }
                .seating-legend { margin-top: 20px; padding: 15px; background: #f0f0f0; border-radius: 5px; border: 1px solid #ddd; }
                .legend-items { display: flex; flex-wrap: wrap; gap: 15px; justify-content: center; margin-bottom: 10px; }
                .legend-item { display: flex; align-items: center; gap: 5px; font-weight: bold; }
                .legend-color { display: inline-block; width: 20px; height: 20px; border-radius: 3px; }
                .legend-color.set-a { background: #e57373; border: 2px solid #c62828; }
                .legend-color.set-b { background: #81c784; border: 2px solid #2e7d32; }
                .legend-color.set-c { background: #64b5f6; border: 2px solid #1565c0; }
                .legend-color.set-d { background: #fff176; border: 2px solid #f9a825; }
                @media print { body { padding: 0; } }
            </style>
        </head>
        <body>
            <h1>Exam Seating Visualization</h1>
            <div class="details">
                <p><strong>Exam:</strong> ${selectedExam.name}</p>
                <p><strong>Date:</strong> ${selectedExam.date}</p>
                <p><strong>Time:</strong> ${selectedExam.time}</p>
                <p><strong>Duration:</strong> ${selectedExam.duration} minutes</p>
                <p><strong>Room:</strong> ${selectedRoom.name}</p>
                <p><strong>Teacher:</strong> ${selectedRoom.teacher || 'Not assigned'}</p>
            </div>
            <div class="visualization">
                ${visualContainer.innerHTML}
            </div>
            <script>
                window.onload = function() {
                    window.print();
                }
            </script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

// Export visualization to PDF
function exportVisualization(format) {
    const examId = parseInt(document.getElementById('visualExam').value);
    const roomId = parseInt(document.getElementById('visualRoom').value);

    if (isNaN(examId) || isNaN(roomId)) {
        alert('Please select an exam and room first.');
        return;
    }

    const seatingKey = `exam${examId}_room${roomId}`;
    const seatingData = seatingArrangements[seatingKey];

    if (!seatingData) {
        alert('No seating arrangement found for this exam and room.');
        return;
    }

    const selectedExam = exams.find(e => e.id === examId);
    const selectedRoom = rooms.find(r => r.id === roomId);

    if (!selectedExam || !selectedRoom) {
        alert('Invalid exam or room selection.');
        return;
    }

    if (format === 'pdf') {
        // Export to PDF
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('landscape');

        // Add title
        doc.setFontSize(16);
        doc.text(`Exam Seating Visualization: ${selectedExam.name}`, 10, 10);

        // Add exam details
        doc.setFontSize(12);
        doc.text(`Date: ${selectedExam.date}`, 10, 20);
        doc.text(`Time: ${selectedExam.time}`, 70, 20);
        doc.text(`Duration: ${selectedExam.duration} minutes`, 130, 20);
        doc.text(`Room: ${selectedRoom.name}`, 10, 25);
        doc.text(`Teacher: ${selectedRoom.teacher || 'Not assigned'}`, 70, 25);
        doc.text(`Strategy: ${seatingData.strategy || 'Random'}`, 130, 25);

        // Create a visual representation of the room
        doc.setFontSize(14);
        doc.text('Room Layout', 10, 35);

        // Draw the room grid
        const startX = 10;
        const startY = 45;
        const cellWidth = 25;
        const cellHeight = 15;

        // Create a 2D array to represent the room
        const roomGrid = Array(selectedRoom.rows).fill().map(() => Array(selectedRoom.columns).fill(null));

        // Fill in the seats with student data
        seatingData.seats.forEach(seat => {
            if (seat.row < selectedRoom.rows && seat.col < selectedRoom.columns) {
                roomGrid[seat.row][seat.col] = seat;
            }
        });

        // Draw the grid
        for (let row = 0; row < selectedRoom.rows; row++) {
            for (let col = 0; col < selectedRoom.columns; col++) {
                const x = startX + col * cellWidth;
                const y = startY + row * cellHeight;

                // Draw cell border
                doc.rect(x, y, cellWidth, cellHeight);

                // Get students in this bench
                const benchStudents = [];
                seatingData.seats.forEach(seat => {
                    if (seat.row === row && seat.col === col) {
                        const student = students.find(s => s.id === seat.studentId);
                        if (student) {
                            benchStudents.push({
                                id: student.id,
                                set: seat.set
                            });
                        }
                    }
                });

                // Add bench label
                doc.setFontSize(6);
                doc.text(`R${row+1}C${col+1}`, x + 1, y + 4);

                // Add student IDs and sets
                if (benchStudents.length > 0) {
                    let studentY = y + 8;
                    benchStudents.forEach(student => {
                        let text = student.id;
                        if (student.set) {
                            text += ` (Set ${student.set})`;
                        }
                        doc.text(text, x + 1, studentY);
                        studentY += 3;
                    });
                }
            }
        }

        // Add legend and set totals for set-wise arrangement
        if (seatingData.strategy === 'setwise') {
            const legendY = startY + selectedRoom.rows * cellHeight + 10;
            doc.setFontSize(10);
            doc.text('Set Legend:', 10, legendY);
            doc.text('Set A - Red', 10, legendY + 5);
            doc.text('Set B - Green', 50, legendY + 5);
            doc.text('Set C - Blue', 90, legendY + 5);
            doc.text('Set D - Yellow', 130, legendY + 5);

            // Count students by set
            const setCounts = { 'A': 0, 'B': 0, 'C': 0, 'D': 0 };

            seatingData.seats.forEach(seat => {
                if (seat.set && setCounts.hasOwnProperty(seat.set)) {
                    setCounts[seat.set]++;
                }
            });

            // Add set totals
            doc.setFontSize(10);
            doc.text('Set Totals:', 10, legendY + 15);
            let setTotalText = '';
            for (const set in setCounts) {
                if (setCounts[set] > 0) {
                    setTotalText += `Set ${set}: ${setCounts[set]} students   `;
                }
            }
            doc.text(setTotalText, 10, legendY + 20);
        }

        // Save the PDF
        doc.save(`visualization_${selectedExam.name}_${selectedRoom.name}.pdf`);
    }
}

// Generate seating for a specific room
function generateSeatingForRoom(examId, roomId, studentsForRoom, strategy) {
    const selectedRoom = rooms.find(r => r.id === roomId);
    if (!selectedRoom) return false;

    const seats = [];
    const studentsPerBench = selectedRoom.studentsPerBench || 1;

    switch(strategy) {
        case 'random':
            // Shuffle students randomly
            const shuffledStudents = [...studentsForRoom].sort(() => Math.random() - 0.5);

            // Assign students to benches
            for (let i = 0; i < shuffledStudents.length; i++) {
                const benchIndex = Math.floor(i / studentsPerBench);
                const positionInBench = i % studentsPerBench;

                const row = Math.floor(benchIndex / selectedRoom.columns);
                const col = benchIndex % selectedRoom.columns;

                if (row < selectedRoom.rows && col < selectedRoom.columns) {
                    seats.push({
                        row,
                        col,
                        studentId: shuffledStudents[i].id,
                        position: positionInBench
                    });
                }
            }
            break;

        case 'setwise':
            // Divide students into 4 sets
            const setSize = Math.ceil(studentsForRoom.length / 4);
            const setA = studentsForRoom.slice(0, setSize);
            const setB = studentsForRoom.slice(setSize, setSize * 2);
            const setC = studentsForRoom.slice(setSize * 2, setSize * 3);
            const setD = studentsForRoom.slice(setSize * 3);

            // Group students by set
            const setGroups = {
                'A': setA,
                'B': setB,
                'C': setC,
                'D': setD
            };

            // Get total number of students
            const totalStudents = studentsForRoom.length;

            // Create a pattern for set distribution within benches
            // This ensures different sets within each bench and no similar sets adjacent
            const setPatterns = [
                ['A', 'B', 'C', 'D'], // Pattern for bench 1
                ['B', 'C', 'D', 'A'], // Pattern for bench 2
                ['C', 'D', 'A', 'B'], // Pattern for bench 3
                ['D', 'A', 'B', 'C']  // Pattern for bench 4
            ];

            // Assign students to benches
            let benchCount = 0;
            let setCounters = { 'A': 0, 'B': 0, 'C': 0, 'D': 0 };

            for (let row = 0; row < selectedRoom.rows; row++) {
                for (let col = 0; col < selectedRoom.columns; col++) {
                    // Get the pattern for this bench
                    const patternIndex = benchCount % setPatterns.length;
                    const currentPattern = setPatterns[patternIndex];

                    // Assign students to positions within this bench
                    for (let position = 0; position < studentsPerBench; position++) {
                        // Get the set for this position
                        const set = currentPattern[position % currentPattern.length];

                        // Get a student from this set if available
                        if (setGroups[set] && setCounters[set] < setGroups[set].length) {
                            const student = setGroups[set][setCounters[set]];
                            setCounters[set]++;

                            seats.push({
                                row,
                                col,
                                studentId: student.id,
                                set: set,
                                position: position
                            });
                        }
                    }

                    benchCount++;

                    // If we've assigned all students, break out
                    if (seats.length >= totalStudents) {
                        break;
                    }
                }

                // If we've assigned all students, break out
                if (seats.length >= totalStudents) {
                    break;
                }
            }
            break;

        default:
            // For other strategies, use a simple row-by-row assignment
            for (let i = 0; i < studentsForRoom.length; i++) {
                const benchIndex = Math.floor(i / studentsPerBench);
                const positionInBench = i % studentsPerBench;

                const row = Math.floor(benchIndex / selectedRoom.columns);
                const col = benchIndex % selectedRoom.columns;

                if (row < selectedRoom.rows && col < selectedRoom.columns) {
                    seats.push({
                        row,
                        col,
                        studentId: studentsForRoom[i].id,
                        position: positionInBench
                    });
                }
            }
    }

    // Save the seating arrangement
    seatingArrangements[`exam${examId}_room${roomId}`] = {
        examId,
        roomId,
        seats,
        strategy
    };

    return true;
}

// Load authorized users from localStorage
function loadAuthorizedUsers() {
    const storedAuthorizedStudents = localStorage.getItem('authorizedStudents');
    const storedAuthorizedTeachers = localStorage.getItem('authorizedTeachers');

    if (storedAuthorizedStudents) {
        authorizedStudents = JSON.parse(storedAuthorizedStudents);
    }

    if (storedAuthorizedTeachers) {
        authorizedTeachers = JSON.parse(storedAuthorizedTeachers);
    }
}

// Save authorized users to localStorage
function saveAuthorizedUsers() {
    localStorage.setItem('authorizedStudents', JSON.stringify(authorizedStudents));
    localStorage.setItem('authorizedTeachers', JSON.stringify(authorizedTeachers));
}

// Upload authorized users
function uploadAuthorizedUsers(e) {
    e.preventDefault();

    const userType = document.getElementById('userType').value;
    const fileInput = document.getElementById('authorizedFile');
    const file = fileInput.files[0];

    if (!file) {
        alert('Please select a file to upload');
        return;
    }

    const reader = new FileReader();

    reader.onload = function(e) {
        try {
            let newUsers = [];

            if (file.name.endsWith('.csv')) {
                // Parse CSV
                const csvData = e.target.result;
                const lines = csvData.split('\n');

                // Skip header row
                for (let i = 1; i < lines.length; i++) {
                    if (lines[i].trim() === '') continue; // Skip empty lines

                    const values = lines[i].split(',');

                    if (values.length >= 4) {
                        if (userType === 'student') {
                            // Format for students: Serial No, Name, Email, USN/Roll Number, Course
                            const [, name, email, id, course] = values;

                            if (name && email && id) {
                                const courseId = parseInt(course) || 1;
                                newUsers.push({ id, name, email, course: courseId, registered: false });
                            }
                        } else {
                            // Format for teachers: Serial No, Name, Email, ID, Department
                            const [, name, email, id, department] = values;

                            if (name && email && id) {
                                newUsers.push({ id, name, email, department: department || 'General', registered: false });
                            }
                        }
                    }
                }
            } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                // Parse Excel
                const workbook = XLSX.read(e.target.result, { type: 'array' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                // Skip header row
                for (let i = 1; i < data.length; i++) {
                    if (!data[i] || data[i].length === 0) continue; // Skip empty rows

                    if (data[i].length >= 4) {
                        if (userType === 'student') {
                            // Format for students: Serial No, Name, Email, USN/Roll Number, Course
                            const [, name, email, id, course] = data[i];

                            if (name && email && id) {
                                const courseId = parseInt(course) || 1;
                                newUsers.push({ id, name, email, course: courseId, registered: false });
                            }
                        } else {
                            // Format for teachers: Serial No, Name, Email, ID, Department
                            const [, name, email, id, department] = data[i];

                            if (name && email && id) {
                                newUsers.push({ id, name, email, department: department || 'General', registered: false });
                            }
                        }
                    }
                }
            } else {
                alert('Unsupported file format. Please upload a CSV or Excel file.');
                return;
            }

            if (newUsers.length === 0) {
                alert('No valid users found in the file.');
                return;
            }

            // Check for existing users and update the registered status
            if (userType === 'student') {
                // Get existing registered users
                const users = JSON.parse(localStorage.getItem('users') || '[]');

                // Update registered status for new users
                newUsers.forEach(newUser => {
                    const existingUser = users.find(user => user.email === newUser.email);
                    if (existingUser) {
                        newUser.registered = true;
                    }

                    // Check if user already exists in authorized list
                    const existingAuthorized = authorizedStudents.find(user => user.email === newUser.email);
                    if (existingAuthorized) {
                        // Update existing user
                        existingAuthorized.name = newUser.name;
                        existingAuthorized.id = newUser.id;
                        existingAuthorized.course = newUser.course;
                    } else {
                        // Add new user
                        authorizedStudents.push(newUser);
                    }
                });
            } else {
                // Get existing registered users
                const users = JSON.parse(localStorage.getItem('users') || '[]');

                // Update registered status for new users
                newUsers.forEach(newUser => {
                    const existingUser = users.find(user => user.email === newUser.email);
                    if (existingUser) {
                        newUser.registered = true;
                    }

                    // Check if user already exists in authorized list
                    const existingAuthorized = authorizedTeachers.find(user => user.email === newUser.email);
                    if (existingAuthorized) {
                        // Update existing user
                        existingAuthorized.name = newUser.name;
                        existingAuthorized.id = newUser.id;
                        existingAuthorized.department = newUser.department;
                    } else {
                        // Add new user
                        authorizedTeachers.push(newUser);
                    }
                });
            }

            // Save to localStorage
            saveAuthorizedUsers();

            // Render tables
            renderAuthorizedUsersTable();

            // Reset form
            document.getElementById('authorizedUsersForm').reset();

            alert(`Successfully uploaded ${newUsers.length} ${userType}s.`);
        } catch (error) {
            console.error('Error parsing file:', error);
            alert('Error parsing file. Please check the file format and try again.');
        }
    };

    if (file.name.endsWith('.xlsx')) {
        reader.readAsArrayBuffer(file);
    } else {
        reader.readAsText(file);
    }
}

// Render authorized users tables
function renderAuthorizedUsersTable() {
    const studentsTable = document.getElementById('authorizedStudentsTable');
    const teachersTable = document.getElementById('authorizedTeachersTable');

    if (studentsTable) {
        const tbody = studentsTable.querySelector('tbody');
        tbody.innerHTML = '';

        authorizedStudents.forEach(student => {
            const courseName = courses.find(c => c.id === student.course)?.name || 'Unknown';

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${student.name}</td>
                <td>${student.email}</td>
                <td>${student.id}</td>
                <td>${courseName}</td>
                <td>${student.registered ? '<span class="status-registered">Yes</span>' : '<span class="status-pending">No</span>'}</td>
                <td>
                    <button onclick="removeAuthorizedUser('student', '${student.email}')" class="btn-delete">Remove</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    if (teachersTable) {
        const tbody = teachersTable.querySelector('tbody');
        tbody.innerHTML = '';

        authorizedTeachers.forEach(teacher => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${teacher.name}</td>
                <td>${teacher.email}</td>
                <td>${teacher.department}</td>
                <td>${teacher.registered ? '<span class="status-registered">Yes</span>' : '<span class="status-pending">No</span>'}</td>
                <td>
                    <button onclick="removeAuthorizedUser('teacher', '${teacher.email}')" class="btn-delete">Remove</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }
}

// Remove authorized user
function removeAuthorizedUser(type, email) {
    if (confirm(`Are you sure you want to remove this ${type} from the authorized list?`)) {
        if (type === 'student') {
            authorizedStudents = authorizedStudents.filter(user => user.email !== email);
        } else {
            authorizedTeachers = authorizedTeachers.filter(user => user.email !== email);
        }

        // Save to localStorage
        saveAuthorizedUsers();

        // Render tables
        renderAuthorizedUsersTable();
    }
}

// Update template info based on selected user type
function updateTemplateInfo() {
    const userType = document.getElementById('userType').value;
    const templateInfo = document.getElementById('templateInfo');

    if (templateInfo) {
        if (userType === 'student') {
            templateInfo.innerHTML = '<p><strong>Student Template Format:</strong> Serial No, Name, Email, USN/Roll Number, Course</p>';
        } else {
            templateInfo.innerHTML = '<p><strong>Teacher Template Format:</strong> Serial No, Name, Email, ID, Department</p>';
        }
    }
}