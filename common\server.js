/**
 * Data Storage Module
 * Handles data storage using localStorage
 */

// Always use demo mode since we've removed the PHP backend
let demoModeEnabled = true;

// Demo data structure
const demoData = {
    users: [],
    students: [],
    teachers: [],
    courses: [],
    rooms: [],
    exams: [],
    seatingArrangements: [],
    authorizedStudents: [],
    authorizedTeachers: []
};

// Initialize demo data with sample values
function initDemoData() {
    // Sample courses
    demoData.courses = [
        { id: 1, name: 'Computer Science' },
        { id: 2, name: 'Artificial Intelligence' },
        { id: 3, name: 'Physics' },
        { id: 4, name: 'Chemistry' },
        { id: 5, name: 'Biology' },
        { id: 6, name: 'Mathematics' },
        { id: 7, name: 'Data Science' }
    ];

    // Sample rooms
    demoData.rooms = [
        { id: 1, name: 'Room 101', rows: 5, columns: 5, students_per_bench: 4, capacity: 100, teacher: '<PERSON>' },
        { id: 2, name: 'Room 102', rows: 4, columns: 4, students_per_bench: 4, capacity: 64, teacher: '<PERSON>' },
        { id: 3, name: 'Room 103', rows: 6, columns: 6, students_per_bench: 2, capacity: 72, teacher: '<PERSON>' },
        { id: 4, name: 'Auditorium A', rows: 8, columns: 10, students_per_bench: 3, capacity: 240, teacher: '<PERSON> <PERSON>' }
    ];

    // Sample exams
    demoData.exams = [
        { id: 1, name: 'Midterm Exam', date: '2025-06-15', time: '09:00:00', duration: 120 },
        { id: 2, name: 'Final Exam', date: '2025-07-30', time: '10:00:00', duration: 180 },
        { id: 3, name: 'Quiz 1', date: '2025-06-01', time: '14:00:00', duration: 60 }
    ];

    // Sample students
    demoData.students = [
        { id: 'STUD001', name: 'Test Student', email: '<EMAIL>', course_id: 1 },
        { id: 'STUD002', name: 'John Doe', email: '<EMAIL>', course_id: 1 },
        { id: 'STUD003', name: 'Jane Smith', email: '<EMAIL>', course_id: 2 },
        { id: 'STUD004', name: 'Robert Brown', email: '<EMAIL>', course_id: 1 },
        { id: 'STUD005', name: 'Emily Wilson', email: '<EMAIL>', course_id: 3 },
        { id: 'STUD006', name: 'Michael Johnson', email: '<EMAIL>', course_id: 2 },
        { id: 'STUD007', name: 'Sarah Davis', email: '<EMAIL>', course_id: 4 },
        { id: 'STUD008', name: 'David Miller', email: '<EMAIL>', course_id: 5 },
        { id: 'STUD009', name: 'Lisa Garcia', email: '<EMAIL>', course_id: 3 },
        { id: 'STUD010', name: 'James Wilson', email: '<EMAIL>', course_id: 1 },
        { id: 'STUD011', name: 'Jennifer Lee', email: '<EMAIL>', course_id: 6 },
        { id: 'STUD012', name: 'Daniel Clark', email: '<EMAIL>', course_id: 7 },
        { id: 'STUD013', name: 'Michelle White', email: '<EMAIL>', course_id: 5 },
        { id: 'STUD014', name: 'Christopher Martin', email: '<EMAIL>', course_id: 4 },
        { id: 'STUD015', name: 'Amanda Taylor', email: '<EMAIL>', course_id: 2 }
    ];

    // Users
    demoData.users = [
        {
            id: 1,
            name: 'Administrator',
            email: '<EMAIL>',
            password: 'admin@1',
            role: 'admin'
        },
        {
            id: 2,
            name: 'Test Student',
            email: '<EMAIL>',
            password: '123',
            role: 'student',
            studentId: 'STUD001'
        }
    ];

    // Sample authorized students
    demoData.authorizedStudents = demoData.students.map(student => ({
        id: student.id,
        name: student.name,
        email: student.email,
        user_id: student.id,
        type: 'student',
        course_id: student.course_id,
        registered: student.id === 'STUD001' // Only the test student is registered
    }));

    // Sample authorized teachers
    demoData.authorizedTeachers = [
        { id: 'TEACH001', name: 'John Doe', email: '<EMAIL>', user_id: 'TEACH001', type: 'teacher', department: 'Computer Science', registered: false },
        { id: 'TEACH002', name: 'Jane Smith', email: '<EMAIL>', user_id: 'TEACH002', type: 'teacher', department: 'Physics', registered: false },
        { id: 'TEACH003', name: 'Robert Johnson', email: '<EMAIL>', user_id: 'TEACH003', type: 'teacher', department: 'Chemistry', registered: false }
    ];

    // Save to localStorage
    saveAllDemoData();
}

// Save all demo data to localStorage
function saveAllDemoData() {
    Object.keys(demoData).forEach(key => {
        localStorage.setItem(key, JSON.stringify(demoData[key]));
    });
}

// Get demo data
function getDemoData(key) {
    return demoData[key] || [];
}

// Save demo data
function saveDemoData(key, data) {
    demoData[key] = data;
    localStorage.setItem(key, JSON.stringify(data));
    return { success: true, message: 'Data saved successfully' };
}

// Check server connectivity (always returns false since we've removed the PHP backend)
async function checkServerConnectivity() {
    // Always enable demo mode
    if (!demoModeEnabled) {
        enableDemoMode();
    }

    // Update UI to show current mode
    updateModeIndicator();

    return false;
}

// Enable demo mode
function enableDemoMode() {
    demoModeEnabled = true;

    // Initialize demo data if not already done
    if (!localStorage.getItem('demoInitialized')) {
        initDemoData();
        localStorage.setItem('demoInitialized', 'true');
    }

    console.info('Demo mode enabled. Using localStorage for data storage.');
}

// Update UI to show current mode
function updateModeIndicator() {
    // Create or update mode indicator
    let indicator = document.getElementById('mode-indicator');

    if (!indicator) {
        indicator = document.createElement('div');
        indicator.id = 'mode-indicator';
        indicator.style.position = 'fixed';
        indicator.style.bottom = '10px';
        indicator.style.right = '10px';
        indicator.style.padding = '5px 10px';
        indicator.style.borderRadius = '5px';
        indicator.style.fontSize = '12px';
        indicator.style.fontWeight = 'bold';
        indicator.style.zIndex = '9999';
        document.body.appendChild(indicator);
    }

    // Always show OFFLINE MODE
    indicator.textContent = 'OFFLINE MODE';
    indicator.style.backgroundColor = '#ff9800';
    indicator.style.color = 'white';
}

// Check server connectivity on page load
document.addEventListener('DOMContentLoaded', function() {
    checkServerConnectivity();
});

// Export functions
window.Server = {
    checkServerConnectivity,
    isServerConnected: () => false, // Always return false since we've removed the PHP backend
    isDemoMode: () => true, // Always return true since we're always in demo mode
    getDemoData,
    saveDemoData,
    enableDemoMode
};
