/**
 * Data Module
 * Handles data management and storage using localStorage
 */

// Data keys mapping
const DATA_KEYS = {
    'students': 'students',
    'exams': 'exams',
    'rooms': 'rooms',
    'courses': 'courses',
    'authorizedStudents': 'authorizedStudents',
    'authorizedTeachers': 'authorizedTeachers',
    'seatingArrangements': 'seatingArrangements',
    'users': 'users'
};

// These functions are no longer needed since we're using localStorage only
// Kept as empty functions for compatibility with existing code
async function fetchData(endpoint, params = {}) {
    console.warn('fetchData is deprecated. Using localStorage instead.');
    return [];
}

async function postData(endpoint, data) {
    console.warn('postData is deprecated. Using localStorage instead.');
    return { success: true };
}

// Get data from localStorage
async function getData(key, params = {}) {
    // Always get data from localStorage
    return getDataFromLocalStorage(key, params);
}

// Get data from localStorage
function getDataFromLocalStorage(key, params = {}) {
    const dataKey = DATA_KEYS[key];

    if (!dataKey) {
        console.error(`Unknown data key for localStorage: ${key}`);
        return [];
    }

    // Get data from localStorage
    const storedData = localStorage.getItem(dataKey);

    if (!storedData) {
        return [];
    }

    const data = JSON.parse(storedData);

    // Apply filters based on params
    if (Object.keys(params).length > 0) {
        return data.filter(item => {
            return Object.keys(params).every(param => {
                if (param === 'type' && key.includes('authorized')) {
                    return true; // Already handled by the key name
                }
                return item[param] === params[param];
            });
        });
    }

    return data;
}

// Save data to localStorage
async function saveData(key, data, action = 'add') {
    // Always save data to localStorage
    return saveDataToLocalStorage(key, data, action);
}

// Save data to localStorage
function saveDataToLocalStorage(key, data, action = 'add') {
    const dataKey = DATA_KEYS[key];

    if (!dataKey) {
        console.error(`Unknown data key for localStorage: ${key}`);
        throw new Error(`Unknown data key: ${key}`);
    }

    // Get existing data
    let existingData = [];
    const storedData = localStorage.getItem(dataKey);

    if (storedData) {
        existingData = JSON.parse(storedData);
    }

    let result;

    // Handle different actions
    switch (action) {
        case 'add':
            // For arrays of data (like authorizedStudents)
            if (Array.isArray(data)) {
                // Add each item with a new ID if needed
                data.forEach(item => {
                    if (!item.id) {
                        item.id = Date.now() + Math.floor(Math.random() * 1000);
                    }
                    existingData.push(item);
                });
            } else {
                // Add single item with a new ID if needed
                if (!data.id) {
                    data.id = Date.now() + Math.floor(Math.random() * 1000);
                }
                existingData.push(data);
            }
            result = { success: true, id: Array.isArray(data) ? null : data.id };
            break;

        case 'update':
            // Update existing item
            const index = existingData.findIndex(item => item.id === data.id);
            if (index !== -1) {
                existingData[index] = { ...existingData[index], ...data };
                result = { success: true };
            } else {
                result = { success: false, error: 'Item not found' };
            }
            break;

        case 'delete':
            // Delete item
            const newData = existingData.filter(item => item.id !== data.id);
            existingData = newData;
            result = { success: true };
            break;

        case 'generate':
            // Special case for seating arrangements
            if (key === 'seatingArrangements') {
                // Add seating arrangement with a new ID if needed
                if (!data.id) {
                    data.id = Date.now() + Math.floor(Math.random() * 1000);
                }
                existingData.push(data);
                result = { success: true, id: data.id };
            } else {
                result = { success: false, error: 'Invalid action for this key' };
            }
            break;

        default:
            result = { success: false, error: 'Invalid action' };
    }

    // Save updated data
    localStorage.setItem(dataKey, JSON.stringify(existingData));

    return result;
}

// Delete data from API
async function deleteData(key, id) {
    return await saveData(key, { id }, 'delete');
}

// Update data in API
async function updateData(key, data) {
    return await saveData(key, data, 'update');
}

// Load sample data (for development/testing)
function loadSampleData() {
    console.log('Loading sample data...');

    // Check if server is connected
    window.Server.checkServerConnectivity().then(isConnected => {
        if (!isConnected) {
            console.log('Server not connected. Using demo mode with localStorage.');
        }
    });
}

// Export functions
window.Data = {
    getData,
    saveData,
    deleteData,
    updateData,
    loadSampleData
};
