/**
 * Students Module
 * Handles student management functionality
 */

// Initialize students module
function initStudents() {
    // Add event listener for student form
    const studentForm = document.getElementById('studentForm');
    if (studentForm) {
        studentForm.addEventListener('submit', addStudent);
    }
    
    // Add event listener for upload form
    const uploadForm = document.getElementById('uploadForm');
    if (uploadForm) {
        uploadForm.addEventListener('submit', uploadStudents);
    }
    
    // Add event listener for delete selected button
    const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
    if (deleteSelectedBtn) {
        deleteSelectedBtn.addEventListener('click', deleteSelectedStudents);
    }
    
    // Add event listener for select all checkbox
    const selectAllCheckbox = document.getElementById('selectAllStudents');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('input[name="selectedStudents"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }
    
    // Render students table
    renderStudentsTable();
}

// Add new student
function addStudent(e) {
    e.preventDefault();
    
    const students = window.Data.getData('students');
    
    const student = {
        id: document.getElementById('studentId').value,
        name: document.getElementById('studentName').value,
        email: document.getElementById('studentEmail').value,
        course: parseInt(document.getElementById('studentCourse').value)
    };
    
    // Check if student ID already exists
    if (students.some(s => s.id === student.id)) {
        alert('Student ID already exists. Please use a different ID.');
        return;
    }
    
    students.push(student);
    window.Data.saveData('students', students);
    
    renderStudentsTable();
    
    // Reset form
    document.getElementById('studentForm').reset();
}

// Upload students from CSV or Excel file
function uploadStudents(e) {
    e.preventDefault();
    
    const fileInput = document.getElementById('studentFile');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('Please select a file to upload');
        return;
    }
    
    const reader = new FileReader();
    
    reader.onload = function(e) {
        try {
            let newStudents = [];
            
            if (file.name.endsWith('.csv')) {
                // Parse CSV
                const csvData = e.target.result;
                const lines = csvData.split('\n');
                
                // Skip header row
                for (let i = 1; i < lines.length; i++) {
                    if (lines[i].trim() === '') continue; // Skip empty lines
                    
                    const values = lines[i].split(',');
                    
                    if (values.length >= 4) {
                        // Format: Serial No, Name, USN/Roll Number, Email, Course
                        const [, name, id, email, courseId] = values;
                        
                        if (name && id && email) {
                            const course = parseInt(courseId) || 1;
                            newStudents.push({ id, name, email, course });
                        }
                    }
                }
            } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                // Parse Excel
                const workbook = XLSX.read(e.target.result, { type: 'array' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                
                // Skip header row
                for (let i = 1; i < data.length; i++) {
                    if (!data[i] || data[i].length === 0) continue; // Skip empty rows
                    
                    if (data[i].length >= 4) {
                        // Format: Serial No, Name, USN/Roll Number, Email, Course
                        const [, name, id, email, courseId] = data[i];
                        
                        if (name && id && email) {
                            const course = parseInt(courseId) || 1;
                            newStudents.push({ id, name, email, course });
                        }
                    }
                }
            } else {
                alert('Unsupported file format. Please upload a CSV or Excel file.');
                return;
            }
            
            if (newStudents.length === 0) {
                alert('No valid students found in the file.');
                return;
            }
            
            // Get existing students
            const students = window.Data.getData('students');
            
            // Check for duplicate IDs
            const duplicates = [];
            newStudents.forEach(newStudent => {
                if (students.some(s => s.id === newStudent.id)) {
                    duplicates.push(newStudent.id);
                }
            });
            
            if (duplicates.length > 0) {
                if (!confirm(`The following student IDs already exist: ${duplicates.join(', ')}. Do you want to update these students?`)) {
                    return;
                }
                
                // Update existing students
                newStudents.forEach(newStudent => {
                    const index = students.findIndex(s => s.id === newStudent.id);
                    if (index !== -1) {
                        students[index] = newStudent;
                    } else {
                        students.push(newStudent);
                    }
                });
            } else {
                // Add all new students
                students.push(...newStudents);
            }
            
            window.Data.saveData('students', students);
            
            renderStudentsTable();
            
            // Reset form
            document.getElementById('uploadForm').reset();
            
            alert(`Successfully uploaded ${newStudents.length} students.`);
        } catch (error) {
            console.error('Error parsing file:', error);
            alert('Error parsing file. Please check the file format and try again.');
        }
    };
    
    if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        reader.readAsArrayBuffer(file);
    } else {
        reader.readAsText(file);
    }
}

// Render students table
function renderStudentsTable() {
    const table = document.getElementById('studentsTable');
    if (!table) return;
    
    const tbody = table.querySelector('tbody');
    tbody.innerHTML = '';
    
    const students = window.Data.getData('students');
    const courses = window.Data.getData('courses');
    
    students.forEach(student => {
        const course = courses.find(c => c.id === student.course);
        const courseName = course ? course.name : 'Unknown';
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><input type="checkbox" name="selectedStudents" value="${student.id}"></td>
            <td>${student.id}</td>
            <td>${student.name}</td>
            <td>${student.email}</td>
            <td>${courseName}</td>
            <td>
                <button onclick="window.Students.editStudent('${student.id}')" class="btn-edit">Edit</button>
                <button onclick="window.Students.deleteStudent('${student.id}')" class="btn-delete">Delete</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Edit student
function editStudent(id) {
    const students = window.Data.getData('students');
    const student = students.find(s => s.id === id);
    
    if (!student) {
        alert('Student not found');
        return;
    }
    
    // Create modal for editing
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <h3>Edit Student</h3>
            <form id="editStudentForm">
                <div class="form-group">
                    <label for="editStudentName">Full Name</label>
                    <input type="text" id="editStudentName" value="${student.name}" required>
                </div>
                <div class="form-group">
                    <label for="editStudentId">Roll Number/ID</label>
                    <input type="text" id="editStudentId" value="${student.id}" required>
                </div>
                <div class="form-group">
                    <label for="editStudentEmail">Email</label>
                    <input type="email" id="editStudentEmail" value="${student.email}" required>
                </div>
                <div class="form-group">
                    <label for="editStudentCourse">Course</label>
                    <select id="editStudentCourse" required>
                        <option value="">-- Select Course --</option>
                        ${window.Data.getData('courses').map(course => 
                            `<option value="${course.id}" ${student.course === course.id ? 'selected' : ''}>${course.name}</option>`
                        ).join('')}
                    </select>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn">Save Changes</button>
                    <button type="button" class="btn btn-cancel" onclick="window.Utils.closeModal()">Cancel</button>
                </div>
            </form>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Handle form submission
    document.getElementById('editStudentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const oldId = student.id;
        const newId = document.getElementById('editStudentId').value;
        const name = document.getElementById('editStudentName').value;
        const email = document.getElementById('editStudentEmail').value;
        const course = parseInt(document.getElementById('editStudentCourse').value);
        
        // Check if new ID already exists (if changed)
        if (oldId !== newId && students.some(s => s.id === newId)) {
            alert('Student ID already exists. Please use a different ID.');
            return;
        }
        
        // Update student data
        student.id = newId;
        student.name = name;
        student.email = email;
        student.course = course;
        
        // If ID changed, update references in seating arrangements
        if (oldId !== newId) {
            const seatingArrangements = window.Data.getData('seatingArrangements');
            
            // Update all seating arrangements that reference this student
            for (const key in seatingArrangements) {
                const arrangement = seatingArrangements[key];
                arrangement.seats.forEach(seat => {
                    if (seat.studentId === oldId) {
                        seat.studentId = newId;
                    }
                });
            }
            
            window.Data.saveData('seatingArrangements', seatingArrangements);
        }
        
        window.Data.saveData('students', students);
        
        renderStudentsTable();
        
        window.Utils.closeModal();
    });
}

// Delete student
function deleteStudent(id) {
    if (confirm('Are you sure you want to delete this student?')) {
        let students = window.Data.getData('students');
        students = students.filter(s => s.id !== id);
        
        window.Data.saveData('students', students);
        
        // Remove student from seating arrangements
        const seatingArrangements = window.Data.getData('seatingArrangements');
        
        for (const key in seatingArrangements) {
            const arrangement = seatingArrangements[key];
            arrangement.seats = arrangement.seats.filter(seat => seat.studentId !== id);
        }
        
        window.Data.saveData('seatingArrangements', seatingArrangements);
        
        renderStudentsTable();
    }
}

// Delete selected students
function deleteSelectedStudents() {
    const checkboxes = document.querySelectorAll('input[name="selectedStudents"]:checked');
    
    if (checkboxes.length === 0) {
        alert('Please select at least one student to delete.');
        return;
    }
    
    if (confirm(`Are you sure you want to delete ${checkboxes.length} selected students?`)) {
        const selectedIds = Array.from(checkboxes).map(checkbox => checkbox.value);
        
        let students = window.Data.getData('students');
        students = students.filter(s => !selectedIds.includes(s.id));
        
        window.Data.saveData('students', students);
        
        // Remove students from seating arrangements
        const seatingArrangements = window.Data.getData('seatingArrangements');
        
        for (const key in seatingArrangements) {
            const arrangement = seatingArrangements[key];
            arrangement.seats = arrangement.seats.filter(seat => !selectedIds.includes(seat.studentId));
        }
        
        window.Data.saveData('seatingArrangements', seatingArrangements);
        
        renderStudentsTable();
        
        // Uncheck "select all" checkbox
        const selectAllCheckbox = document.getElementById('selectAllStudents');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
        }
    }
}

// Export functions
window.Students = {
    initStudents,
    addStudent,
    uploadStudents,
    renderStudentsTable,
    editStudent,
    deleteStudent,
    deleteSelectedStudents
};
