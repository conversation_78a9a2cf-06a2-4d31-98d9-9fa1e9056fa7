/* User dashboard specific styles */
.dashboard {
    margin-top: 20px;
    background: white;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    padding: 20px;
}

.user-info {
    margin-bottom: 20px;
    padding: 15px;
    background: #f0f7ff;
    border-radius: 5px;
}

.user-info h2 {
    margin-top: 0;
    color: #333;
}

.seating-info {
    margin-top: 30px;
}

.seating-info h3 {
    margin-bottom: 15px;
    color: #333;
}

/* Table styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background: #f4f4f4;
    font-weight: bold;
}

tr:hover {
    background: #f9f9f9;
}

.btn-logout {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 3px;
    cursor: pointer;
    float: right;
}

/* Visualization styles */
.seat-visualization {
    margin-top: 30px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 5px;
}

.room-visual {
    margin-top: 20px;
    border: 1px solid #ddd;
    padding: 20px;
    min-height: 300px;
    background: white;
}

.seating-grid {
    display: grid;
    gap: 10px;
    margin-top: 20px;
}

.seat {
    background: #e0e0e0;
    border-radius: 5px;
    padding: 10px;
    text-align: center;
    min-height: 80px;
}

.seat.current-student {
    background: #ffeb3b; /* Highlight current student's seat */
    border: 2px solid #ffc107;
}

.seat.occupied {
    background: #b8e0d2;
}

.seat-number {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.student-id {
    font-weight: bold;
    margin-bottom: 3px;
}

.student-name {
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.teacher-desk {
    width: 120px;
    height: 60px;
    margin: 20px auto;
    background: #d1c4e9;
    text-align: center;
    line-height: 60px;
    border-radius: 3px;
}

.room-info {
    margin-bottom: 20px;
    padding: 10px;
    background: #e9f5ff;
    border-radius: 5px;
}
