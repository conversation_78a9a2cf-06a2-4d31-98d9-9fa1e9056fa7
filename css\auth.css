/**
 * Authentication CSS
 * Styles for login and registration pages
 */

/* Auth container */
.auth-container {
    max-width: 500px;
    margin: 50px auto;
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* Tabs */
.tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
}

.tab-btn {
    padding: 10px 15px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    opacity: 0.7;
}

.tab-btn.active {
    opacity: 1;
    border-bottom: 2px solid #007bff;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Student fields */
.student-fields {
    display: none;
    transition: all 0.3s ease;
}

/* Direct links */
.direct-links {
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid #eee;
    font-size: 14px;
    color: #666;
}

/* Template info */
.template-info {
    margin: 10px 0;
    padding: 10px;
    background-color: #f8f9fa;
    border-left: 4px solid #17a2b8;
    border-radius: 4px;
}

.template-info p {
    margin: 0;
    font-size: 14px;
}

/* Template download */
.template-download {
    margin-top: 10px;
    font-size: 14px;
}
