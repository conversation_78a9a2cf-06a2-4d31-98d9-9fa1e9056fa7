/**
 * <PERSON>ams Module
 * Handles exam management functionality
 */

// Initialize exams module
function initExams() {
    // Add event listener for exam form
    const examForm = document.getElementById('examForm');
    if (examForm) {
        examForm.addEventListener('submit', addExam);
    }
    
    // Render exams table
    renderExamsTable();
}

// Add new exam
function addExam(e) {
    e.preventDefault();
    
    const exams = window.Data.getData('exams');
    
    const exam = {
        id: exams.length > 0 ? Math.max(...exams.map(e => e.id)) + 1 : 1,
        name: document.getElementById('examName').value,
        date: document.getElementById('examDate').value,
        time: document.getElementById('examTime').value,
        duration: document.getElementById('examDuration').value
    };
    
    exams.push(exam);
    window.Data.saveData('exams', exams);
    
    renderExamsTable();
    
    // Reset form
    document.getElementById('examForm').reset();
    
    // Reload dropdowns
    window.Dashboard.loadExamsDropdown('seatingExam');
    window.Dashboard.loadExamsDropdown('visualExam');
}

// Render exams table
function renderExamsTable() {
    const table = document.getElementById('examsTable');
    if (!table) return;
    
    const tbody = table.querySelector('tbody');
    tbody.innerHTML = '';
    
    const exams = window.Data.getData('exams');
    
    exams.forEach(exam => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${exam.name}</td>
            <td>${exam.date}</td>
            <td>${exam.time}</td>
            <td>${exam.duration} min</td>
            <td>
                <button onclick="window.Exams.editExam(${exam.id})" class="btn-edit">Edit</button>
                <button onclick="window.Exams.deleteExam(${exam.id})" class="btn-delete">Delete</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Edit exam
function editExam(id) {
    const exams = window.Data.getData('exams');
    const exam = exams.find(e => e.id === id);
    
    if (!exam) {
        alert('Exam not found');
        return;
    }
    
    // Create modal for editing
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <h3>Edit Exam</h3>
            <form id="editExamForm">
                <div class="form-group">
                    <label for="editExamName">Exam Name</label>
                    <input type="text" id="editExamName" value="${exam.name}" required>
                </div>
                <div class="form-group">
                    <label for="editExamDate">Date</label>
                    <input type="date" id="editExamDate" value="${exam.date}" required>
                </div>
                <div class="form-group">
                    <label for="editExamTime">Time</label>
                    <input type="time" id="editExamTime" value="${exam.time}" required>
                </div>
                <div class="form-group">
                    <label for="editExamDuration">Duration (minutes)</label>
                    <input type="number" id="editExamDuration" value="${exam.duration}" required>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn">Save Changes</button>
                    <button type="button" class="btn btn-cancel" onclick="window.Utils.closeModal()">Cancel</button>
                </div>
            </form>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Handle form submission
    document.getElementById('editExamForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        exam.name = document.getElementById('editExamName').value;
        exam.date = document.getElementById('editExamDate').value;
        exam.time = document.getElementById('editExamTime').value;
        exam.duration = document.getElementById('editExamDuration').value;
        
        window.Data.saveData('exams', exams);
        
        renderExamsTable();
        
        // Reload dropdowns
        window.Dashboard.loadExamsDropdown('seatingExam');
        window.Dashboard.loadExamsDropdown('visualExam');
        
        window.Utils.closeModal();
    });
}

// Delete exam
function deleteExam(id) {
    if (confirm('Are you sure you want to delete this exam?')) {
        let exams = window.Data.getData('exams');
        exams = exams.filter(e => e.id !== id);
        
        window.Data.saveData('exams', exams);
        
        renderExamsTable();
        
        // Reload dropdowns
        window.Dashboard.loadExamsDropdown('seatingExam');
        window.Dashboard.loadExamsDropdown('visualExam');
    }
}

// Export functions
window.Exams = {
    initExams,
    addExam,
    renderExamsTable,
    editExam,
    deleteExam
};
