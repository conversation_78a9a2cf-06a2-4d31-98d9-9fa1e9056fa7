<?php
/**
 * API Test Page
 * Tests the API endpoints
 */

// Set content type to HTML
header('Content-Type: text/html');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exam Seating System - API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .success {
            color: #28a745;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            color: #17a2b8;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
        }
        .btn:hover {
            background-color: #0069d9;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .endpoint {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
        }
        .endpoint h3 {
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0069d9;
        }
        .result {
            margin-top: 15px;
            display: none;
        }
    </style>
</head>
<body>
    <h1>Exam Seating System - API Test</h1>
    
    <div class="info">
        <p>This page allows you to test the API endpoints of the Exam Seating System.</p>
        <p>Make sure the database is initialized before testing the API endpoints.</p>
    </div>
    
    <div class="endpoint">
        <h3>Authentication API</h3>
        <div class="form-group">
            <label for="auth-action">Action</label>
            <select id="auth-action">
                <option value="check_auth">Check Authentication</option>
                <option value="admin_login">Admin Login</option>
            </select>
        </div>
        <div class="form-group auth-login-fields" style="display: none;">
            <label for="auth-email">Email</label>
            <input type="email" id="auth-email" value="<EMAIL>">
        </div>
        <div class="form-group auth-login-fields" style="display: none;">
            <label for="auth-password">Password</label>
            <input type="password" id="auth-password" value="admin@1">
        </div>
        <button onclick="testAuthAPI()">Test</button>
        <div id="auth-result" class="result">
            <h4>Result:</h4>
            <pre id="auth-response"></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <h3>Courses API</h3>
        <div class="form-group">
            <label for="courses-action">Action</label>
            <select id="courses-action">
                <option value="get">Get Courses</option>
            </select>
        </div>
        <button onclick="testCoursesAPI()">Test</button>
        <div id="courses-result" class="result">
            <h4>Result:</h4>
            <pre id="courses-response"></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <h3>Exams API</h3>
        <div class="form-group">
            <label for="exams-action">Action</label>
            <select id="exams-action">
                <option value="get">Get Exams</option>
            </select>
        </div>
        <button onclick="testExamsAPI()">Test</button>
        <div id="exams-result" class="result">
            <h4>Result:</h4>
            <pre id="exams-response"></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <h3>Rooms API</h3>
        <div class="form-group">
            <label for="rooms-action">Action</label>
            <select id="rooms-action">
                <option value="get">Get Rooms</option>
            </select>
        </div>
        <button onclick="testRoomsAPI()">Test</button>
        <div id="rooms-result" class="result">
            <h4>Result:</h4>
            <pre id="rooms-response"></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <h3>Students API</h3>
        <div class="form-group">
            <label for="students-action">Action</label>
            <select id="students-action">
                <option value="get">Get Students</option>
            </select>
        </div>
        <button onclick="testStudentsAPI()">Test</button>
        <div id="students-result" class="result">
            <h4>Result:</h4>
            <pre id="students-response"></pre>
        </div>
    </div>
    
    <div>
        <a href="index.html" class="btn">Go to Application</a>
        <a href="server_status.php" class="btn">Server Status</a>
    </div>
    
    <script>
        // Show/hide login fields based on action
        document.getElementById('auth-action').addEventListener('change', function() {
            const loginFields = document.querySelectorAll('.auth-login-fields');
            if (this.value === 'admin_login') {
                loginFields.forEach(field => field.style.display = 'block');
            } else {
                loginFields.forEach(field => field.style.display = 'none');
            }
        });
        
        // Test Authentication API
        async function testAuthAPI() {
            const action = document.getElementById('auth-action').value;
            const resultDiv = document.getElementById('auth-result');
            const responseDiv = document.getElementById('auth-response');
            
            resultDiv.style.display = 'block';
            responseDiv.textContent = 'Loading...';
            
            try {
                let response;
                
                if (action === 'check_auth') {
                    response = await fetch('api/auth.php?action=check_auth');
                } else if (action === 'admin_login') {
                    const email = document.getElementById('auth-email').value;
                    const password = document.getElementById('auth-password').value;
                    
                    response = await fetch('api/auth.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            action: 'admin_login',
                            email: email,
                            password: password
                        })
                    });
                }
                
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                responseDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        // Test Courses API
        async function testCoursesAPI() {
            const resultDiv = document.getElementById('courses-result');
            const responseDiv = document.getElementById('courses-response');
            
            resultDiv.style.display = 'block';
            responseDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('api/courses.php');
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                responseDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        // Test Exams API
        async function testExamsAPI() {
            const resultDiv = document.getElementById('exams-result');
            const responseDiv = document.getElementById('exams-response');
            
            resultDiv.style.display = 'block';
            responseDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('api/exams.php');
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                responseDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        // Test Rooms API
        async function testRoomsAPI() {
            const resultDiv = document.getElementById('rooms-result');
            const responseDiv = document.getElementById('rooms-response');
            
            resultDiv.style.display = 'block';
            responseDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('api/rooms.php');
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                responseDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        // Test Students API
        async function testStudentsAPI() {
            const resultDiv = document.getElementById('students-result');
            const responseDiv = document.getElementById('students-response');
            
            resultDiv.style.display = 'block';
            responseDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('api/students.php');
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                responseDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
